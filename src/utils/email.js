const nodemailer = require('nodemailer');
const logger = require('../config/logger');

// Create transporter for sending emails
const createTransporter = () => {
  // For development, we'll use ethereal.email to simulate email sending
  // In production, you would use your actual email service (Gmail, SendGrid, etc.)
  if (process.env.NODE_ENV === 'development') {
    return nodemailer.createTransporter({
      host: 'smtp.ethereal.email',
      port: 587,
      secure: false,
      auth: {
        user: '<EMAIL>',
        pass: 'your-ethereal-password'
      }
    });
  } else {
    // For production, you would configure your actual email service
    // Example for Gmail:
    /*
    return nodemailer.createTransporter({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    });
    */
    
    // Example for SendGrid:
    /*
    return nodemailer.createTransporter({
      service: 'SendGrid',
      auth: {
        user: 'apikey',
        pass: process.env.SENDGRID_API_KEY
      }
    });
    */
    
    // For now, we'll return a mock transporter for production
    return {
      sendMail: async (mailOptions) => {
        logger.info('Email would be sent in production', { 
          to: mailOptions.to,
          subject: mailOptions.subject
        });
        return { messageId: 'mock-message-id' };
      }
    };
  }
};

// Send appointment notification email
const sendAppointmentNotification = async (appointment, type) => {
  try {
    // Only send emails if EMAIL_API_KEY is set
    if (!process.env.EMAIL_API_KEY) {
      logger.info('Email API key not set, skipping email notification', { 
        appointmentId: appointment._id,
        type: type
      });
      return;
    }

    const transporter = createTransporter();
    
    const patientName = `${appointment.patient.first_name} ${appointment.patient.last_name}`;
    const doctorName = `Dr. ${appointment.doctor.first_name} ${appointment.doctor.last_name}`;
    
    let subject, text, html;
    
    if (type === 'created') {
      subject = `Appointment Confirmation - ${appointment.appointment_date}`;
      text = `Dear ${patientName},
      
Your appointment with ${doctorName} on ${new Date(appointment.appointment_date).toLocaleDateString()} at ${appointment.appointment_time} has been confirmed.
      
Reason: ${appointment.reason}
      
Please arrive 15 minutes early for your appointment.
      
Thank you,
Hospital Management System`;
      
      html = `<p>Dear ${patientName},</p>
      
<p>Your appointment with ${doctorName} on ${new Date(appointment.appointment_date).toLocaleDateString()} at ${appointment.appointment_time} has been confirmed.</p>
      
<p><strong>Reason:</strong> ${appointment.reason}</p>
      
<p>Please arrive 15 minutes early for your appointment.</p>
      
<p>Thank you,<br>
Hospital Management System</p>`;
    } else if (type === 'cancelled') {
      subject = `Appointment Cancelled - ${appointment.appointment_date}`;
      text = `Dear ${patientName},
      
Your appointment with ${doctorName} on ${new Date(appointment.appointment_date).toLocaleDateString()} at ${appointment.appointment_time} has been cancelled.
      
Reason: ${appointment.reason}
      
We apologize for any inconvenience. Please contact us to reschedule your appointment.
      
Thank you,
Hospital Management System`;
      
      html = `<p>Dear ${patientName},</p>
      
<p>Your appointment with ${doctorName} on ${new Date(appointment.appointment_date).toLocaleDateString()} at ${appointment.appointment_time} has been cancelled.</p>
      
<p><strong>Reason:</strong> ${appointment.reason}</p>
      
<p>We apologize for any inconvenience. Please contact us to reschedule your appointment.</p>
      
<p>Thank you,<br>
Hospital Management System</p>`;
    } else {
      logger.warn('Invalid email notification type', { type });
      return;
    }
    
    const mailOptions = {
      from: process.env.ADMIN_EMAIL || '<EMAIL>',
      to: appointment.patient.email,
      subject: subject,
      text: text,
      html: html
    };
    
    const info = await transporter.sendMail(mailOptions);
    
    logger.info('Appointment notification email sent', { 
      appointmentId: appointment._id,
      type: type,
      messageId: info.messageId
    });
    
    return info;
  } catch (error) {
    logger.error('Error sending appointment notification email', { 
      error: error.message, 
      stack: error.stack,
      appointmentId: appointment._id,
      type: type
    });
    throw error;
  }
};

// Send daily KPI report email
const sendDailyKpiReport = async (reportData, recipients) => {
  try {
    // Only send emails if EMAIL_API_KEY is set
    if (!process.env.EMAIL_API_KEY) {
      logger.info('Email API key not set, skipping daily KPI report email');
      return;
    }

    const transporter = createTransporter();
    
    const subject = `Daily KPI Report - ${new Date(reportData.date).toLocaleDateString()}`;
    
    const text = `Daily KPI Report for ${new Date(reportData.date).toLocaleDateString()}:
    
New Patients: ${reportData.new_patients}
Total Appointments: ${reportData.appointments_total}
Completed Appointments: ${reportData.appointments_completed}
Open Admissions: ${reportData.admissions_open}
Available Beds: ${reportData.beds_available}
Revenue: KES ${reportData.revenue.toFixed(2)}
AR Balance: KES ${reportData.accounts_receivable_balance.toFixed(2)}
M-Pesa Total: KES ${reportData.mpesa_total_confirmed.toFixed(2)}

Thank you,
Hospital Management System`;
    
    // Create HTML table for the report
    const html = `<p>Daily KPI Report for ${new Date(reportData.date).toLocaleDateString()}:</p>
    
<table border="1" cellpadding="5" cellspacing="0">
  <tr>
    <th>Metric</th>
    <th>Value</th>
  </tr>
  <tr>
    <td>New Patients</td>
    <td>${reportData.new_patients}</td>
  </tr>
  <tr>
    <td>Total Appointments</td>
    <td>${reportData.appointments_total}</td>
  </tr>
  <tr>
    <td>Completed Appointments</td>
    <td>${reportData.appointments_completed}</td>
  </tr>
  <tr>
    <td>Open Admissions</td>
    <td>${reportData.admissions_open}</td>
  </tr>
  <tr>
    <td>Available Beds</td>
    <td>${reportData.beds_available}</td>
  </tr>
  <tr>
    <td>Revenue</td>
    <td>KES ${reportData.revenue.toFixed(2)}</td>
  </tr>
  <tr>
    <td>AR Balance</td>
    <td>KES ${reportData.accounts_receivable_balance.toFixed(2)}</td>
  </tr>
  <tr>
    <td>M-Pesa Total</td>
    <td>KES ${reportData.mpesa_total_confirmed.toFixed(2)}</td>
  </tr>
</table>

<p>Thank you,<br>
Hospital Management System</p>`;
    
    const mailOptions = {
      from: process.env.ADMIN_EMAIL || '<EMAIL>',
      to: recipients,
      subject: subject,
      text: text,
      html: html
    };
    
    const info = await transporter.sendMail(mailOptions);
    
    logger.info('Daily KPI report email sent', { 
      date: reportData.date,
      recipients: recipients,
      messageId: info.messageId
    });
    
    return info;
  } catch (error) {
    logger.error('Error sending daily KPI report email', { 
      error: error.message, 
      stack: error.stack
    });
    throw error;
  }
};

module.exports = {
  sendAppointmentNotification,
  sendDailyKpiReport
};