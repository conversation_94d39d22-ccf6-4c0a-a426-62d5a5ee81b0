const express = require('express');
const router = express.Router();
const { 
  getAllAppointments,
  getAppointmentById,
  getCalendarAppointments,
  createAppointment,
  updateAppointment,
  deleteAppointment
} = require('../controllers/appointments.controller');
const { authenticateJWT, authorizeRole } = require('../config/auth');

// Apply authentication middleware to all routes
router.use(authenticateJWT);

// GET /appointments - Get all appointments with pagination and search
router.get('/', authorizeRole(['admin', 'doctor', 'nurse', 'clerk']), getAllAppointments);

// GET /appointments/calendar - Get appointments for calendar view
router.get('/calendar', authorizeRole(['admin', 'doctor', 'nurse', 'clerk']), getCalendarAppointments);

// GET /appointments/:id - Get appointment by ID
router.get('/:id', authorizeRole(['admin', 'doctor', 'nurse', 'clerk']), getAppointmentById);

// POST /appointments - Create new appointment
router.post('/', authorizeRole(['admin', 'clerk']), createAppointment);

// PUT /appointments/:id - Update appointment
router.put('/:id', authorizeRole(['admin', 'doctor', 'clerk']), updateAppointment);

// DELETE /appointments/:id - Delete appointment
router.delete('/:id', authorizeRole(['admin', 'clerk']), deleteAppointment);

module.exports = router;