const express = require('express');
const router = express.Router();
const { 
  getAllAdmissions,
  getAdmissionById,
  createAdmission,
  dischargePatient,
  updateAdmission,
  deleteAdmission
} = require('../controllers/admissions.controller');
const { authenticateJWT, authorizeRole } = require('../config/auth');

// Apply authentication middleware to all routes
router.use(authenticateJWT);

// GET /admissions - Get all admissions with pagination and search
router.get('/', authorizeRole(['admin', 'doctor', 'nurse']), getAllAdmissions);

// GET /admissions/:id - Get admission by ID
router.get('/:id', authorizeRole(['admin', 'doctor', 'nurse']), getAdmissionById);

// POST /admissions - Create new admission
router.post('/', authorizeRole(['admin', 'doctor']), createAdmission);

// PUT /admissions/:id/discharge - Discharge patient
router.put('/:id/discharge', authorizeRole(['admin', 'doctor']), dischargePatient);

// PUT /admissions/:id - Update admission
router.put('/:id', authorizeRole(['admin', 'doctor']), updateAdmission);

// DELETE /admissions/:id - Delete admission
router.delete('/:id', authorizeRole(['admin']), deleteAdmission);

module.exports = router;