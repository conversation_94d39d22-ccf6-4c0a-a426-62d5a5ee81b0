const express = require('express');
const router = express.Router();
const { 
  getAllStaff,
  getStaffById,
  createStaff,
  updateStaff,
  deleteStaff
} = require('../controllers/staff.controller');
const { authenticateJWT, authorizeRole } = require('../config/auth');

// Apply authentication middleware to all routes
router.use(authenticateJWT);

// GET /staff - Get all staff with pagination and search
router.get('/', authorizeRole(['admin']), getAllStaff);

// GET /staff/:id - Get staff by ID
router.get('/:id', authorizeRole(['admin']), getStaffById);

// POST /staff - Create new staff
router.post('/', authorizeRole(['admin']), createStaff);

// PUT /staff/:id - Update staff
router.put('/:id', authorizeRole(['admin']), updateStaff);

// DELETE /staff/:id - Delete staff
router.delete('/:id', authorizeRole(['admin']), deleteStaff);

module.exports = router;