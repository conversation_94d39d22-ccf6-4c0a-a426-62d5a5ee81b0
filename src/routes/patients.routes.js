const express = require('express');
const router = express.Router();
const { 
  getAllPatients,
  getPatientById,
  createPatient,
  updatePatient,
  deletePatient
} = require('../controllers/patients.controller');
const { authenticateJWT, authorizeRole } = require('../config/auth');

// Apply authentication middleware to all routes
router.use(authenticateJWT);

// GET /patients - Get all patients with pagination and search
router.get('/', authorizeRole(['admin', 'doctor', 'nurse', 'clerk']), getAllPatients);

// GET /patients/:id - Get patient by ID
router.get('/:id', authorizeRole(['admin', 'doctor', 'nurse', 'clerk']), getPatientById);

// POST /patients - Create new patient
router.post('/', authorizeRole(['admin', 'clerk']), createPatient);

// PUT /patients/:id - Update patient
router.put('/:id', authorizeRole(['admin', 'clerk']), updatePatient);

// DELETE /patients/:id - Delete patient
router.delete('/:id', authorizeRole(['admin']), deletePatient);

module.exports = router;