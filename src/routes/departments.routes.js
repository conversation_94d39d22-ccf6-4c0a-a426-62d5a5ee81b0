const express = require('express');
const router = express.Router();
const { 
  getAllDepartments,
  getDepartmentById,
  createDepartment,
  updateDepartment,
  deleteDepartment
} = require('../controllers/departments.controller');
const { authenticateJWT, authorizeRole } = require('../config/auth');

// Apply authentication middleware to all routes
router.use(authenticateJWT);

// GET /departments - Get all departments with pagination and search
router.get('/', authorizeRole(['admin', 'doctor', 'nurse', 'clerk']), getAllDepartments);

// GET /departments/:id - Get department by ID
router.get('/:id', authorizeRole(['admin', 'doctor', 'nurse', 'clerk']), getDepartmentById);

// POST /departments - Create new department
router.post('/', authorizeRole(['admin']), createDepartment);

// PUT /departments/:id - Update department
router.put('/:id', authorizeRole(['admin']), updateDepartment);

// DELETE /departments/:id - Delete department
router.delete('/:id', authorizeRole(['admin']), deleteDepartment);

module.exports = router;