const express = require('express');
const router = express.Router();
const { 
  getAllSuppliers,
  getSupplierById,
  createSupplier,
  updateSupplier,
  deleteSupplier
} = require('../controllers/suppliers.controller');
const { authenticateJWT, authorizeRole } = require('../config/auth');

// Apply authentication middleware to all routes
router.use(authenticateJWT);

// GET /suppliers - Get all suppliers with pagination and search
router.get('/', authorizeRole(['admin', 'clerk']), getAllSuppliers);

// GET /suppliers/:id - Get supplier by ID
router.get('/:id', authorizeRole(['admin', 'clerk']), getSupplierById);

// POST /suppliers - Create new supplier
router.post('/', authorizeRole(['admin', 'clerk']), createSupplier);

// PUT /suppliers/:id - Update supplier
router.put('/:id', authorizeRole(['admin', 'clerk']), updateSupplier);

// DELETE /suppliers/:id - Delete supplier
router.delete('/:id', authorizeRole(['admin']), deleteSupplier);

module.exports = router;