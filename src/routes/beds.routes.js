const express = require('express');
const router = express.Router();
const { 
  getAllBeds,
  getBedById,
  getAvailableBeds,
  createBed,
  updateBed,
  deleteBed
} = require('../controllers/beds.controller');
const { authenticateJWT, authorizeRole } = require('../config/auth');

// Apply authentication middleware to all routes
router.use(authenticateJWT);

// GET /beds - Get all beds with pagination and search
router.get('/', authorizeRole(['admin', 'doctor', 'nurse']), getAllBeds);

// GET /beds/available - Get available beds
router.get('/available', authorizeRole(['admin', 'doctor', 'nurse']), getAvailableBeds);

// GET /beds/:id - Get bed by ID
router.get('/:id', authorizeRole(['admin', 'doctor', 'nurse']), getBedById);

// POST /beds - Create new bed
router.post('/', authorizeRole(['admin']), createBed);

// PUT /beds/:id - Update bed
router.put('/:id', authorizeRole(['admin']), updateBed);

// DELETE /beds/:id - Delete bed
router.delete('/:id', authorizeRole(['admin']), deleteBed);

module.exports = router;