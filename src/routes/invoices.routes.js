const express = require('express');
const router = express.Router();
const { 
  getAllInvoices,
  getInvoiceById,
  createInvoice,
  updateInvoice,
  deleteInvoice
} = require('../controllers/invoices.controller');
const { authenticateJWT, authorizeRole } = require('../config/auth');

// Apply authentication middleware to all routes
router.use(authenticateJWT);

// GET /invoices - Get all invoices with pagination and search
router.get('/', authorizeRole(['admin', 'clerk']), getAllInvoices);

// GET /invoices/:id - Get invoice by ID
router.get('/:id', authorizeRole(['admin', 'clerk']), getInvoiceById);

// POST /invoices - Create new invoice
router.post('/', authorizeRole(['admin', 'clerk']), createInvoice);

// PUT /invoices/:id - Update invoice
router.put('/:id', authorizeRole(['admin', 'clerk']), updateInvoice);

// DELETE /invoices/:id - Delete invoice
router.delete('/:id', authorizeRole(['admin']), deleteInvoice);

module.exports = router;