const express = require('express');
const router = express.Router();
const { 
  getAllPayments,
  getPaymentById,
  createPayment,
  updatePayment,
  deletePayment
} = require('../controllers/payments.controller');
const { authenticateJWT, authorizeRole } = require('../config/auth');

// Apply authentication middleware to all routes
router.use(authenticateJWT);

// GET /payments - Get all payments with pagination and search
router.get('/', authorizeRole(['admin', 'clerk']), getAllPayments);

// GET /payments/:id - Get payment by ID
router.get('/:id', authorizeRole(['admin', 'clerk']), getPaymentById);

// POST /payments - Create new payment
router.post('/', authorizeRole(['admin', 'clerk']), createPayment);

// PUT /payments/:id - Update payment
router.put('/:id', authorizeRole(['admin']), updatePayment);

// DELETE /payments/:id - Delete payment
router.delete('/:id', authorizeRole(['admin']), deletePayment);

module.exports = router;