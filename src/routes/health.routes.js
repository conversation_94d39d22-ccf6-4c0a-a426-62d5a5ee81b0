const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');

// GET /health - Health check endpoint
router.get('/', (req, res) => {
  const healthCheck = {
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    database: mongoose.connection.readyState === 1 ? 'Connected' : 'Disconnected',
  };

  res.status(200).json(healthCheck);
});

module.exports = router;