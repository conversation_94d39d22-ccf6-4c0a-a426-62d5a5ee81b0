const express = require('express');
const router = express.Router();
const { 
  getAllLabOrders,
  getLabOrderById,
  createLabOrder,
  updateLabOrder,
  deleteLabOrder
} = require('../controllers/lab-orders.controller');
const { authenticateJWT, authorizeRole } = require('../config/auth');

// Apply authentication middleware to all routes
router.use(authenticateJWT);

// GET /lab-orders - Get all lab orders with pagination and search
router.get('/', authorizeRole(['admin', 'doctor', 'nurse']), getAllLabOrders);

// GET /lab-orders/:id - Get lab order by ID
router.get('/:id', authorize<PERSON>ole(['admin', 'doctor', 'nurse']), getLabOrderById);

// POST /lab-orders - Create new lab order
router.post('/', authorizeRole(['admin', 'doctor']), createLabOrder);

// PUT /lab-orders/:id - Update lab order (add results)
router.put('/:id', authorizeRole(['admin', 'doctor']), updateLabOrder);

// DELETE /lab-orders/:id - Delete lab order
router.delete('/:id', authorizeRole(['admin']), deleteLabOrder);

module.exports = router;