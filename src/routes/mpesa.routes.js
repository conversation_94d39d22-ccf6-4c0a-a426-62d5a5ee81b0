const express = require('express');
const router = express.Router();
const { 
  initiateMpesaPayment,
  handleMpesaCallback
} = require('../controllers/mpesa.controller');
const { authenticateJWT, authorizeRole } = require('../config/auth');

// POST /payments/mpesa/initiate - Initiate M-Pesa STK Push
router.post('/payments/initiate', authenticateJWT, authorizeRole(['admin', 'clerk']), initiateMpesaPayment);

// POST /payments/mpesa/callback - Handle M-Pesa callback (public endpoint)
router.post('/payments/callback', handleMpesaCallback);

// GET /receipts - Get M-Pesa receipts (already implemented in payments.routes.js)
// For simplicity, we'll keep the receipts endpoint in payments.routes.js

module.exports = router;