const express = require('express');
const router = express.Router();
const { 
  getAllInventory,
  getInventoryById,
  getLowStockItems,
  createInventoryItem,
  updateInventoryItem,
  adjustInventoryQuantity,
  deleteInventoryItem
} = require('../controllers/inventory.controller');
const { authenticateJWT, authorizeRole } = require('../config/auth');

// Apply authentication middleware to all routes
router.use(authenticateJWT);

// GET /inventory - Get all inventory items with pagination and search
router.get('/', authorizeRole(['admin', 'doctor', 'nurse', 'clerk']), getAllInventory);

// GET /inventory/low-stock - Get low stock items
router.get('/low-stock', authorizeRole(['admin', 'doctor', 'nurse', 'clerk']), getLowStockItems);

// GET /inventory/:id - Get inventory item by ID
router.get('/:id', authorizeRole(['admin', 'doctor', 'nurse', 'clerk']), getInventoryById);

// POST /inventory - Create new inventory item
router.post('/', authorizeRole(['admin', 'clerk']), createInventoryItem);

// PUT /inventory/:id - Update inventory item
router.put('/:id', authorizeRole(['admin', 'clerk']), updateInventoryItem);

// PUT /inventory/:id/adjust - Adjust inventory quantity
router.put('/:id/adjust', authorizeRole(['admin', 'clerk']), adjustInventoryQuantity);

// DELETE /inventory/:id - Delete inventory item
router.delete('/:id', authorizeRole(['admin']), deleteInventoryItem);

module.exports = router;