const express = require('express');
const router = express.Router();
const { 
  getDailyReports,
  getDailyReportById,
  getLast7DaysKPIs,
  exportDailyReports,
  generateDailyReport
} = require('../controllers/reports.controller');
const { authenticateJWT, authorizeRole } = require('../config/auth');

// Apply authentication middleware to all routes except export
router.use(authenticateJWT);

// GET /reports/daily - Get daily reports with pagination and search
router.get('/daily', authorizeRole(['admin']), getDailyReports);

// GET /reports/daily/:id - Get daily report by ID
router.get('/daily/:id', authorizeRole(['admin']), getDailyReportById);

// GET /reports/daily/kpis - Get last 7 days KPIs
router.get('/daily/kpis', authorizeRole(['admin', 'doctor']), getLast7DaysKPIs);

// GET /reports/daily/export - Export daily reports as CSV
router.get('/daily/export', authorizeRole(['admin']), exportDailyReports);

// POST /reports/daily/generate - Generate daily report (internal use)
router.post('/daily/generate', authorizeRole(['admin']), generateDailyReport);

module.exports = router;