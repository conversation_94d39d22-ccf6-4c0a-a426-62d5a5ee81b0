const jwt = require('jsonwebtoken');

const generateToken = (payload) => {
  return jwt.sign(payload, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
  });
};

const verifyToken = (token) => {
  return jwt.verify(token, process.env.JWT_SECRET);
};

const authenticateJWT = (req, res, next) => {
  const authHeader = req.header('Authorization');
  const apiKey = req.header('X-API-Key');

  // Check for API Key
  if (apiKey) {
    if (apiKey !== process.env.API_KEY) {
      return res.status(401).json({ error: 'Invalid API Key' });
    }
    req.user = { role: 'api' };
    return next();
  }

  // Check for JWT Token
  if (!authHeader) {
    return res.status(401).json({ error: 'Access denied. No token provided.' });
  }

  const token = authHeader.replace('Bearer ', '');
  if (!token) {
    return res.status(401).json({ error: 'Access denied. Token invalid format.' });
  }

  try {
    const decoded = verifyToken(token);
    req.user = decoded;
    next();
  } catch (error) {
    res.status(400).json({ error: 'Invalid token.' });
  }
};

const authorizeRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Access denied. No user found.' });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ error: 'Access denied. Insufficient permissions.' });
    }

    next();
  };
};

module.exports = {
  generateToken,
  verifyToken,
  authenticateJWT,
  authorizeRole,
};