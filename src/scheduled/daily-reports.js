const cron = require('node-cron');
const ReportDaily = require('../models/ReportDaily');
const Patient = require('../models/Patient');
const Appointment = require('../models/Appointment');
const Admission = require('../models/Admission');
const Bed = require('../models/Bed');
const Invoice = require('../models/Invoice');
const Payment = require('../models/Payment');
const MpesaReceipt = require('../models/MpesaReceipt');
const logger = require('../config/logger');
const nodemailer = require('nodemailer');

// Helper function to calculate daily KPIs
const calculateDailyKPIs = async (date) => {
  try {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);
    
    // New patients count
    const newPatients = await Patient.countDocuments({
      created_at: {
        $gte: startOfDay,
        $lte: endOfDay,
      },
    });
    
    // Appointments count
    const appointmentsTotal = await Appointment.countDocuments({
      appointment_date: {
        $gte: startOfDay,
        $lte: endOfDay,
      },
    });
    
    // Completed appointments count
    const appointmentsCompleted = await Appointment.countDocuments({
      appointment_date: {
        $gte: startOfDay,
        $lte: endOfDay,
      },
      status: 'completed',
    });
    
    // Open admissions count
    const admissionsOpen = await Admission.countDocuments({
      admission_date: {
        $lte: endOfDay,
      },
      $or: [
        { discharge_date: { $exists: false } },
        { discharge_date: { $gt: endOfDay } },
      ],
    });
    
    // Available beds count
    const bedsAvailable = await Bed.countDocuments({
      status: 'available',
      is_active: true,
    });
    
    // Revenue (sum of payments for the day)
    const payments = await Payment.aggregate([
      {
        $match: {
          payment_date: {
            $gte: startOfDay,
            $lte: endOfDay,
          },
        },
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$amount' },
        },
      },
    ]);
    
    const revenue = payments.length > 0 ? payments[0].total : 0;
    
    // Accounts receivable balance (sum of unpaid invoice balances)
    const invoices = await Invoice.aggregate([
      {
        $match: {
          status: { $ne: 'paid' },
        },
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$balance_due' },
        },
      },
    ]);
    
    const accountsReceivableBalance = invoices.length > 0 ? invoices[0].total : 0;
    
    // M-Pesa confirmed payments total
    const mpesaPayments = await MpesaReceipt.aggregate([
      {
        $match: {
          transaction_date: {
            $gte: startOfDay,
            $lte: endOfDay,
          },
          status: 'completed',
        },
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$amount' },
        },
      },
    ]);
    
    const mpesaTotalConfirmed = mpesaPayments.length > 0 ? mpesaPayments[0].total : 0;
    
    return {
      new_patients: newPatients,
      appointments_total: appointmentsTotal,
      appointments_completed: appointmentsCompleted,
      admissions_open: admissionsOpen,
      beds_available: bedsAvailable,
      revenue: revenue,
      accounts_receivable_balance: accountsReceivableBalance,
      mpesa_total_confirmed: mpesaTotalConfirmed,
    };
  } catch (error) {
    logger.error('Error calculating daily KPIs', { 
      error: error.message, 
      stack: error.stack 
    });
    throw error;
  }
};

// Function to generate daily report
const generateDailyReport = async (date) => {
  try {
    const reportDate = new Date(date);
    reportDate.setHours(0, 0, 0, 0); // Set to start of day
    
    // Check if report already exists for this date
    const existingReport = await ReportDaily.findOne({ date: reportDate });
    if (existingReport) {
      logger.info('Daily report already exists for this date', { date: reportDate });
      return existingReport;
    }
    
    // Calculate KPIs for the day
    const kpis = await calculateDailyKPIs(reportDate);
    
    // Create new report
    const report = new ReportDaily({
      date: reportDate,
      ...kpis,
    });
    
    await report.save();
    
    logger.info('Daily report generated', { 
      reportId: report._id,
      date: reportDate 
    });
    
    return report;
  } catch (error) {
    logger.error('Error generating daily report', { 
      error: error.message, 
      stack: error.stack 
    });
    throw error;
  }
};

// Function to send daily report via email
const sendDailyReportEmail = async (report) => {
  try {
    // Check if email configuration exists
    if (!process.env.EMAIL_API_KEY || !process.env.ADMIN_EMAIL) {
      logger.info('Email configuration not found, skipping email report');
      return;
    }
    
    // Create transporter
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.ADMIN_EMAIL,
        pass: process.env.EMAIL_API_KEY,
      },
    });
    
    // Format report data for email
    const reportDate = report.date.toISOString().split('T')[0];
    const emailContent = `
      <h2>Daily KPI Report - ${reportDate}</h2>
      <table border="1" cellpadding="5" cellspacing="0">
        <tr>
          <th>Metric</th>
          <th>Value</th>
        </tr>
        <tr>
          <td>New Patients</td>
          <td>${report.new_patients}</td>
        </tr>
        <tr>
          <td>Total Appointments</td>
          <td>${report.appointments_total}</td>
        </tr>
        <tr>
          <td>Completed Appointments</td>
          <td>${report.appointments_completed}</td>
        </tr>
        <tr>
          <td>Open Admissions</td>
          <td>${report.admissions_open}</td>
        </tr>
        <tr>
          <td>Available Beds</td>
          <td>${report.beds_available}</td>
        </tr>
        <tr>
          <td>Revenue</td>
          <td>${report.revenue}</td>
        </tr>
        <tr>
          <td>AR Balance</td>
          <td>${report.accounts_receivable_balance}</td>
        </tr>
        <tr>
          <td>M-Pesa Confirmed</td>
          <td>${report.mpesa_total_confirmed}</td>
        </tr>
      </table>
    `;
    
    // Send email
    const mailOptions = {
      from: process.env.ADMIN_EMAIL,
      to: process.env.ADMIN_EMAIL,
      subject: `Daily KPI Report - ${reportDate}`,
      html: emailContent,
    };
    
    const info = await transporter.sendMail(mailOptions);
    
    logger.info('Daily report email sent', { 
      messageId: info.messageId,
      date: reportDate 
    });
  } catch (error) {
    logger.error('Error sending daily report email', { 
      error: error.message, 
      stack: error.stack 
    });
  }
};

// Schedule daily report generation at 06:00 Africa/Nairobi time
const scheduleDailyReports = () => {
  // Cron expression for 06:00 Africa/Nairobi time
  // Africa/Nairobi is UTC+3, so 06:00 in Nairobi is 03:00 UTC
  cron.schedule('0 3 * * *', async () => {
    try {
      logger.info('Starting daily report generation');
      
      // Generate report for yesterday
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      
      const report = await generateDailyReport(yesterday);
      
      // Send email report
      await sendDailyReportEmail(report);
      
      logger.info('Daily report generation completed');
    } catch (error) {
      logger.error('Error in daily report generation', { 
        error: error.message, 
        stack: error.stack 
      });
    }
  }, {
    timezone: "Africa/Nairobi"
  });
  
  logger.info('Daily reports scheduler started');
};

module.exports = {
  scheduleDailyReports,
  generateDailyReport,
  sendDailyReportEmail,
  calculateDailyKPIs,
};