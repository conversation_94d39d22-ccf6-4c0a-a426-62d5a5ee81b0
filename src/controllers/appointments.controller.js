const Appointment = require('../models/Appointment');
const Patient = require('../models/Patient');
const Staff = require('../models/Staff');
const { sendAppointmentNotification } = require('../utils/email');
const logger = require('../config/logger');

// GET /appointments - Get all appointments with pagination and search
const getAllAppointments = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || '';
    const status = req.query.status || '';
    const dateFrom = req.query.dateFrom || '';
    const dateTo = req.query.dateTo || '';
    const sortBy = req.query.sortBy || 'appointment_date';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;

    // Ensure limit doesn't exceed 100
    const maxLimit = Math.min(limit, 100);

    // Build search query
    const searchQuery = {};
    
    if (search) {
      searchQuery.$or = [
        { reason: { $regex: search, $options: 'i' } },
        { notes: { $regex: search, $options: 'i' } },
      ];
    }
    
    if (status) {
      searchQuery.status = status;
    }
    
    if (dateFrom || dateTo) {
      searchQuery.appointment_date = {};
      if (dateFrom) {
        searchQuery.appointment_date.$gte = new Date(dateFrom);
      }
      if (dateTo) {
        searchQuery.appointment_date.$lte = new Date(dateTo);
      }
    }

    // Get appointments with pagination
    const appointments = await Appointment.find(searchQuery)
      .populate('patient', 'first_name last_name phone')
      .populate('doctor', 'first_name last_name')
      .populate('department', 'name')
      .populate('created_by', 'first_name last_name')
      .sort({ [sortBy]: sortOrder })
      .limit(maxLimit * 1)
      .skip((page - 1) * maxLimit);

    // Get total count for pagination
    const total = await Appointment.countDocuments(searchQuery);

    logger.info('Appointments retrieved', { 
      count: appointments.length, 
      page, 
      limit: maxLimit,
      requestId: req.requestId 
    });

    res.json({
      data: appointments,
      meta: {
        page,
        limit: maxLimit,
        total,
        pages: Math.ceil(total / maxLimit),
      },
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving appointments', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// GET /appointments/:id - Get appointment by ID
const getAppointmentById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const appointment = await Appointment.findById(id)
      .populate('patient', 'first_name last_name phone email')
      .populate('doctor', 'first_name last_name')
      .populate('department', 'name')
      .populate('created_by', 'first_name last_name');
    
    if (!appointment) {
      return res.status(404).json({ error: 'Appointment not found' });
    }

    logger.info('Appointment retrieved', { 
      appointmentId: appointment._id,
      requestId: req.requestId 
    });

    res.json({
      data: appointment,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving appointment', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// GET /appointments/calendar - Get appointments for calendar view
const getCalendarAppointments = async (req, res) => {
  try {
    const startDate = req.query.startDate ? new Date(req.query.startDate) : new Date();
    const endDate = req.query.endDate ? new Date(req.query.endDate) : new Date();
    endDate.setDate(endDate.getDate() + 7); // Default to 1 week

    const appointments = await Appointment.find({
      appointment_date: {
        $gte: startDate,
        $lte: endDate,
      },
      status: { $ne: 'cancelled' },
    })
      .populate('patient', 'first_name last_name')
      .populate('doctor', 'first_name last_name')
      .populate('department', 'name');

    logger.info('Calendar appointments retrieved', { 
      count: appointments.length,
      startDate,
      endDate,
      requestId: req.requestId 
    });

    res.json({
      data: appointments,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving calendar appointments', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// POST /appointments - Create new appointment
const createAppointment = async (req, res) => {
  try {
    const appointmentData = req.body;
    appointmentData.created_by = req.user.id;
    
    // Check for doctor double-booking
    const existingAppointment = await Appointment.findOne({
      doctor: appointmentData.doctor,
      appointment_date: appointmentData.appointment_date,
      appointment_time: appointmentData.appointment_time,
      status: { $ne: 'cancelled' },
    });
    
    if (existingAppointment) {
      return res.status(400).json({ 
        error: 'Doctor already has an appointment at this time' 
      });
    }
    
    // Create new appointment
    const appointment = new Appointment(appointmentData);
    await appointment.save();
    
    // Populate references
    await appointment.populate('patient', 'first_name last_name phone email');
    await appointment.populate('doctor', 'first_name last_name');
    await appointment.populate('department', 'name');
    await appointment.populate('created_by', 'first_name last_name');

    // Send email notification
    try {
      await sendAppointmentNotification(appointment, 'created');
    } catch (emailError) {
      logger.warn('Failed to send appointment creation email', { 
        error: emailError.message,
        appointmentId: appointment._id
      });
    }

    logger.info('Appointment created', { 
      appointmentId: appointment._id,
      requestId: req.requestId 
    });

    res.status(201).json({
      data: appointment,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error creating appointment', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// PUT /appointments/:id - Update appointment
const updateAppointment = async (req, res) => {
  try {
    const { id } = req.params;
    const appointmentData = req.body;
    
    // Check for doctor double-booking if doctor/time is being changed
    if (appointmentData.doctor || appointmentData.appointment_date || appointmentData.appointment_time) {
      const appointmentToUpdate = await Appointment.findById(id);
      if (!appointmentToUpdate) {
        return res.status(404).json({ error: 'Appointment not found' });
      }
      
      const doctorId = appointmentData.doctor || appointmentToUpdate.doctor;
      const appointmentDate = appointmentData.appointment_date || appointmentToUpdate.appointment_date;
      const appointmentTime = appointmentData.appointment_time || appointmentToUpdate.appointment_time;
      
      const existingAppointment = await Appointment.findOne({
        _id: { $ne: id },
        doctor: doctorId,
        appointment_date: appointmentDate,
        appointment_time: appointmentTime,
        status: { $ne: 'cancelled' },
      });
      
      if (existingAppointment) {
        return res.status(400).json({ 
          error: 'Doctor already has an appointment at this time' 
        });
      }
    }
    
    const appointment = await Appointment.findByIdAndUpdate(
      id,
      appointmentData,
      { new: true, runValidators: true }
    )
      .populate('patient', 'first_name last_name phone email');
    
    if (!appointment) {
      return res.status(404).json({ error: 'Appointment not found' });
    }

    // If appointment status is cancelled, send cancellation email
    if (appointment.status === 'cancelled') {
      try {
        await sendAppointmentNotification(appointment, 'cancelled');
      } catch (emailError) {
        logger.warn('Failed to send appointment cancellation email', { 
          error: emailError.message,
          appointmentId: appointment._id
        });
      }
    }

    logger.info('Appointment updated', { 
      appointmentId: appointment._id,
      requestId: req.requestId 
    });

    res.json({
      data: appointment,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error updating appointment', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// DELETE /appointments/:id - Delete appointment
const deleteAppointment = async (req, res) => {
  try {
    const { id } = req.params;
    
    const appointment = await Appointment.findByIdAndDelete(id);
    
    if (!appointment) {
      return res.status(404).json({ error: 'Appointment not found' });
    }

    logger.info('Appointment deleted', { 
      appointmentId: appointment._id,
      requestId: req.requestId 
    });

    res.json({
      data: { message: 'Appointment deleted successfully' },
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error deleting appointment', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  getAllAppointments,
  getAppointmentById,
  getCalendarAppointments,
  createAppointment,
  updateAppointment,
  deleteAppointment,
};