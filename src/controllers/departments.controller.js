const Department = require('../models/Department');
const logger = require('../config/logger');

// GET /departments - Get all departments with pagination and search
const getAllDepartments = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || '';
    const sortBy = req.query.sortBy || 'created_at';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;

    // Ensure limit doesn't exceed 100
    const maxLimit = Math.min(limit, 100);

    // Build search query
    const searchQuery = search
      ? {
          name: { $regex: search, $options: 'i' },
        }
      : {};

    // Get departments with pagination
    const departments = await Department.find(searchQuery)
      .sort({ [sortBy]: sortOrder })
      .limit(maxLimit * 1)
      .skip((page - 1) * maxLimit);

    // Get total count for pagination
    const total = await Department.countDocuments(searchQuery);

    logger.info('Departments retrieved', { 
      count: departments.length, 
      page, 
      limit: maxLimit,
      requestId: req.requestId 
    });

    res.json({
      data: departments,
      meta: {
        page,
        limit: maxLimit,
        total,
        pages: Math.ceil(total / maxLimit),
      },
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving departments', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// GET /departments/:id - Get department by ID
const getDepartmentById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const department = await Department.findById(id);
    
    if (!department) {
      return res.status(404).json({ error: 'Department not found' });
    }

    logger.info('Department retrieved', { 
      departmentId: department._id,
      requestId: req.requestId 
    });

    res.json({
      data: department,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving department', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// POST /departments - Create new department
const createDepartment = async (req, res) => {
  try {
    const departmentData = req.body;
    
    // Create new department
    const department = new Department(departmentData);
    await department.save();

    logger.info('Department created', { 
      departmentId: department._id,
      requestId: req.requestId 
    });

    res.status(201).json({
      data: department,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error creating department', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    
    if (error.code === 11000) {
      return res.status(400).json({ error: 'Department with this name already exists' });
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// PUT /departments/:id - Update department
const updateDepartment = async (req, res) => {
  try {
    const { id } = req.params;
    const departmentData = req.body;
    
    const department = await Department.findByIdAndUpdate(
      id,
      departmentData,
      { new: true, runValidators: true }
    );
    
    if (!department) {
      return res.status(404).json({ error: 'Department not found' });
    }

    logger.info('Department updated', { 
      departmentId: department._id,
      requestId: req.requestId 
    });

    res.json({
      data: department,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error updating department', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    
    if (error.code === 11000) {
      return res.status(400).json({ error: 'Department with this name already exists' });
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// DELETE /departments/:id - Delete department
const deleteDepartment = async (req, res) => {
  try {
    const { id } = req.params;
    
    const department = await Department.findByIdAndDelete(id);
    
    if (!department) {
      return res.status(404).json({ error: 'Department not found' });
    }

    logger.info('Department deleted', { 
      departmentId: department._id,
      requestId: req.requestId 
    });

    res.json({
      data: { message: 'Department deleted successfully' },
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error deleting department', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  getAllDepartments,
  getDepartmentById,
  createDepartment,
  updateDepartment,
  deleteDepartment,
};