const Supplier = require('../models/Supplier');
const logger = require('../config/logger');

// GET /suppliers - Get all suppliers with pagination and search
const getAllSuppliers = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || '';
    const sortBy = req.query.sortBy || 'created_at';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;

    // Ensure limit doesn't exceed 100
    const maxLimit = Math.min(limit, 100);

    // Build search query
    const searchQuery = search
      ? {
          $or: [
            { name: { $regex: search, $options: 'i' } },
            { contact_person: { $regex: search, $options: 'i' } },
            { email: { $regex: search, $options: 'i' } },
            { phone: { $regex: search, $options: 'i' } },
          ],
        }
      : {};

    // Get suppliers with pagination
    const suppliers = await Supplier.find(searchQuery)
      .sort({ [sortBy]: sortOrder })
      .limit(maxLimit * 1)
      .skip((page - 1) * maxLimit);

    // Get total count for pagination
    const total = await Supplier.countDocuments(searchQuery);

    logger.info('Suppliers retrieved', { 
      count: suppliers.length, 
      page, 
      limit: maxLimit,
      requestId: req.requestId 
    });

    res.json({
      data: suppliers,
      meta: {
        page,
        limit: maxLimit,
        total,
        pages: Math.ceil(total / maxLimit),
      },
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving suppliers', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// GET /suppliers/:id - Get supplier by ID
const getSupplierById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const supplier = await Supplier.findById(id);
    
    if (!supplier) {
      return res.status(404).json({ error: 'Supplier not found' });
    }

    logger.info('Supplier retrieved', { 
      supplierId: supplier._id,
      requestId: req.requestId 
    });

    res.json({
      data: supplier,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving supplier', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// POST /suppliers - Create new supplier
const createSupplier = async (req, res) => {
  try {
    const supplierData = req.body;
    
    // Create new supplier
    const supplier = new Supplier(supplierData);
    await supplier.save();

    logger.info('Supplier created', { 
      supplierId: supplier._id,
      requestId: req.requestId 
    });

    res.status(201).json({
      data: supplier,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error creating supplier', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    
    if (error.code === 11000) {
      return res.status(400).json({ error: 'Supplier with this name or phone already exists' });
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// PUT /suppliers/:id - Update supplier
const updateSupplier = async (req, res) => {
  try {
    const { id } = req.params;
    const supplierData = req.body;
    
    const supplier = await Supplier.findByIdAndUpdate(
      id,
      supplierData,
      { new: true, runValidators: true }
    );
    
    if (!supplier) {
      return res.status(404).json({ error: 'Supplier not found' });
    }

    logger.info('Supplier updated', { 
      supplierId: supplier._id,
      requestId: req.requestId 
    });

    res.json({
      data: supplier,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error updating supplier', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    
    if (error.code === 11000) {
      return res.status(400).json({ error: 'Supplier with this name or phone already exists' });
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// DELETE /suppliers/:id - Delete supplier
const deleteSupplier = async (req, res) => {
  try {
    const { id } = req.params;
    
    const supplier = await Supplier.findByIdAndDelete(id);
    
    if (!supplier) {
      return res.status(404).json({ error: 'Supplier not found' });
    }

    logger.info('Supplier deleted', { 
      supplierId: supplier._id,
      requestId: req.requestId 
    });

    res.json({
      data: { message: 'Supplier deleted successfully' },
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error deleting supplier', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  getAllSuppliers,
  getSupplierById,
  createSupplier,
  updateSupplier,
  deleteSupplier,
};