const Inventory = require('../models/Inventory');
const Supplier = require('../models/Supplier');
const logger = require('../config/logger');

// GET /inventory - Get all inventory items with pagination and search
const getAllInventory = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || '';
    const category = req.query.category || '';
    const lowStock = req.query.lowStock === 'true';
    const sortBy = req.query.sortBy || 'created_at';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;

    // Ensure limit doesn't exceed 100
    const maxLimit = Math.min(limit, 100);

    // Build search query
    const searchQuery = {};
    
    if (search) {
      searchQuery.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
      ];
    }
    
    if (category) {
      searchQuery.category = category;
    }
    
    if (lowStock) {
      searchQuery.$expr = { $lte: ['$quantity', '$reorder_level'] };
    }

    // Get inventory items with pagination
    const inventory = await Inventory.find(searchQuery)
      .populate('supplier', 'name contact_person phone')
      .sort({ [sortBy]: sortOrder })
      .limit(maxLimit * 1)
      .skip((page - 1) * maxLimit);

    // Get total count for pagination
    const total = await Inventory.countDocuments(searchQuery);

    logger.info('Inventory items retrieved', { 
      count: inventory.length, 
      page, 
      limit: maxLimit,
      requestId: req.requestId 
    });

    res.json({
      data: inventory,
      meta: {
        page,
        limit: maxLimit,
        total,
        pages: Math.ceil(total / maxLimit),
      },
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving inventory items', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// GET /inventory/:id - Get inventory item by ID
const getInventoryById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const item = await Inventory.findById(id)
      .populate('supplier', 'name contact_person phone');
    
    if (!item) {
      return res.status(404).json({ error: 'Inventory item not found' });
    }

    logger.info('Inventory item retrieved', { 
      itemId: item._id,
      requestId: req.requestId 
    });

    res.json({
      data: item,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving inventory item', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// GET /inventory/low-stock - Get low stock items
const getLowStockItems = async (req, res) => {
  try {
    const items = await Inventory.find({
      $expr: { $lte: ['$quantity', '$reorder_level'] },
      is_active: true
    })
      .populate('supplier', 'name contact_person phone');

    logger.info('Low stock items retrieved', { 
      count: items.length,
      requestId: req.requestId 
    });

    res.json({
      data: items,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving low stock items', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// POST /inventory - Create new inventory item
const createInventoryItem = async (req, res) => {
  try {
    const itemData = req.body;
    
    // Verify supplier exists if provided
    if (itemData.supplier) {
      const supplier = await Supplier.findById(itemData.supplier);
      if (!supplier) {
        return res.status(400).json({ error: 'Supplier not found' });
      }
    }
    
    // Create new inventory item
    const item = new Inventory(itemData);
    await item.save();
    
    // Populate references
    await item.populate('supplier', 'name contact_person phone');

    logger.info('Inventory item created', { 
      itemId: item._id,
      requestId: req.requestId 
    });

    res.status(201).json({
      data: item,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error creating inventory item', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    
    if (error.code === 11000) {
      return res.status(400).json({ error: 'Inventory item with this name already exists' });
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// PUT /inventory/:id - Update inventory item
const updateInventoryItem = async (req, res) => {
  try {
    const { id } = req.params;
    const itemData = req.body;
    
    // Verify supplier exists if being updated
    if (itemData.supplier) {
      const supplier = await Supplier.findById(itemData.supplier);
      if (!supplier) {
        return res.status(400).json({ error: 'Supplier not found' });
      }
    }
    
    const item = await Inventory.findByIdAndUpdate(
      id,
      itemData,
      { new: true, runValidators: true }
    )
      .populate('supplier', 'name contact_person phone');
    
    if (!item) {
      return res.status(404).json({ error: 'Inventory item not found' });
    }

    logger.info('Inventory item updated', { 
      itemId: item._id,
      requestId: req.requestId 
    });

    res.json({
      data: item,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error updating inventory item', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    
    if (error.code === 11000) {
      return res.status(400).json({ error: 'Inventory item with this name already exists' });
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// PUT /inventory/:id/adjust - Adjust inventory quantity
const adjustInventoryQuantity = async (req, res) => {
  try {
    const { id } = req.params;
    const { quantity, reason } = req.body;
    
    if (typeof quantity !== 'number') {
      return res.status(400).json({ error: 'Quantity must be a number' });
    }
    
    const item = await Inventory.findById(id);
    if (!item) {
      return res.status(404).json({ error: 'Inventory item not found' });
    }
    
    // Update quantity
    item.quantity = quantity;
    await item.save();
    
    // Add adjustment note to item history (in a real app, you might want a separate adjustment history)
    if (reason) {
      item.notes = item.notes ? `${item.notes}\nQuantity adjusted: ${reason}` : `Quantity adjusted: ${reason}`;
      await item.save();
    }

    logger.info('Inventory quantity adjusted', { 
      itemId: item._id,
      oldQuantity: item.quantity,
      newQuantity: quantity,
      requestId: req.requestId 
    });

    res.json({
      data: item,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error adjusting inventory quantity', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// DELETE /inventory/:id - Delete inventory item
const deleteInventoryItem = async (req, res) => {
  try {
    const { id } = req.params;
    
    const item = await Inventory.findByIdAndDelete(id);
    
    if (!item) {
      return res.status(404).json({ error: 'Inventory item not found' });
    }

    logger.info('Inventory item deleted', { 
      itemId: item._id,
      requestId: req.requestId 
    });

    res.json({
      data: { message: 'Inventory item deleted successfully' },
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error deleting inventory item', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  getAllInventory,
  getInventoryById,
  getLowStockItems,
  createInventoryItem,
  updateInventoryItem,
  adjustInventoryQuantity,
  deleteInventoryItem,
};