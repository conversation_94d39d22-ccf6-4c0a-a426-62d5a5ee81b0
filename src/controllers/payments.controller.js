const Payment = require('../models/Payment');
const Invoice = require('../models/Invoice');
const Patient = require('../models/Patient');
const Staff = require('../models/Staff');
const MpesaReceipt = require('../models/MpesaReceipt');
const logger = require('../config/logger');

// GET /payments - Get all payments with pagination and search
const getAllPayments = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || '';
    const paymentMethod = req.query.paymentMethod || '';
    const dateFrom = req.query.dateFrom || '';
    const dateTo = req.query.dateTo || '';
    const invoiceId = req.query.invoiceId || '';
    const sortBy = req.query.sortBy || 'payment_date';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;

    // Ensure limit doesn't exceed 100
    const maxLimit = Math.min(limit, 100);

    // Build search query
    const searchQuery = {};
    
    if (search) {
      searchQuery.$or = [
        { reference_number: { $regex: search, $options: 'i' } },
        { notes: { $regex: search, $options: 'i' } },
      ];
    }
    
    if (paymentMethod) {
      searchQuery.payment_method = paymentMethod;
    }
    
    if (invoiceId) {
      searchQuery.invoice = invoiceId;
    }
    
    if (dateFrom || dateTo) {
      searchQuery.payment_date = {};
      if (dateFrom) {
        searchQuery.payment_date.$gte = new Date(dateFrom);
      }
      if (dateTo) {
        searchQuery.payment_date.$lte = new Date(dateTo);
      }
    }

    // Get payments with pagination
    const payments = await Payment.find(searchQuery)
      .populate('invoice', 'invoice_number patient total_amount')
      .populate('patient', 'first_name last_name phone')
      .populate('created_by', 'first_name last_name')
      .sort({ [sortBy]: sortOrder })
      .limit(maxLimit * 1)
      .skip((page - 1) * maxLimit);

    // Get total count for pagination
    const total = await Payment.countDocuments(searchQuery);

    logger.info('Payments retrieved', { 
      count: payments.length, 
      page, 
      limit: maxLimit,
      requestId: req.requestId 
    });

    res.json({
      data: payments,
      meta: {
        page,
        limit: maxLimit,
        total,
        pages: Math.ceil(total / maxLimit),
      },
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving payments', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// GET /payments/:id - Get payment by ID
const getPaymentById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const payment = await Payment.findById(id)
      .populate('invoice', 'invoice_number patient total_amount')
      .populate('patient', 'first_name last_name phone email')
      .populate('created_by', 'first_name last_name');
    
    if (!payment) {
      return res.status(404).json({ error: 'Payment not found' });
    }

    logger.info('Payment retrieved', { 
      paymentId: payment._id,
      requestId: req.requestId 
    });

    res.json({
      data: payment,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving payment', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// POST /payments - Create new payment
const createPayment = async (req, res) => {
  try {
    const paymentData = req.body;
    paymentData.created_by = req.user.id;
    
    // Verify invoice exists
    const invoice = await Invoice.findById(paymentData.invoice);
    if (!invoice) {
      return res.status(400).json({ error: 'Invoice not found' });
    }
    
    // Verify patient exists
    const patient = await Patient.findById(paymentData.patient);
    if (!patient) {
      return res.status(400).json({ error: 'Patient not found' });
    }
    
    // Verify staff exists
    const staff = await Staff.findById(paymentData.created_by);
    if (!staff) {
      return res.status(400).json({ error: 'Staff not found' });
    }
    
    // Check if payment would exceed invoice balance
    const totalPayments = await Payment.aggregate([
      { $match: { invoice: invoice._id } },
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);
    
    const totalPaid = totalPayments.length > 0 ? totalPayments[0].total : 0;
    const newTotalPaid = totalPaid + paymentData.amount;
    
    if (newTotalPaid > invoice.total_amount) {
      return res.status(400).json({ 
        error: 'Payment amount exceeds invoice balance' 
      });
    }
    
    // Create new payment
    const payment = new Payment(paymentData);
    await payment.save();
    
    // Update invoice status
    const balanceAfterPayment = invoice.total_amount - newTotalPaid;
    invoice.amount_paid = newTotalPaid;
    invoice.balance_due = balanceAfterPayment;
    
    if (balanceAfterPayment <= 0) {
      invoice.status = 'paid';
    } else if (invoice.status === 'draft') {
      invoice.status = 'sent';
    }
    
    await invoice.save();
    
    // Populate references
    await payment.populate('invoice', 'invoice_number patient total_amount');
    await payment.populate('patient', 'first_name last_name phone');
    await payment.populate('created_by', 'first_name last_name');

    logger.info('Payment created', { 
      paymentId: payment._id,
      invoiceId: invoice._id,
      amount: paymentData.amount,
      requestId: req.requestId 
    });

    res.status(201).json({
      data: payment,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error creating payment', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// PUT /payments/:id - Update payment
const updatePayment = async (req, res) => {
  try {
    const { id } = req.params;
    const paymentData = req.body;
    
    // Find existing payment
    const existingPayment = await Payment.findById(id);
    if (!existingPayment) {
      return res.status(404).json({ error: 'Payment not found' });
    }
    
    // Prevent updating if it would affect invoice balance calculations
    return res.status(400).json({ 
      error: 'Payments cannot be updated. Please delete and create a new payment.' 
    });
  } catch (error) {
    logger.error('Error updating payment', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// DELETE /payments/:id - Delete payment
const deletePayment = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Find payment
    const payment = await Payment.findById(id);
    if (!payment) {
      return res.status(404).json({ error: 'Payment not found' });
    }
    
    // Find associated invoice
    const invoice = await Invoice.findById(payment.invoice);
    if (invoice) {
      // Update invoice balance
      invoice.amount_paid = invoice.amount_paid - payment.amount;
      invoice.balance_due = invoice.balance_due + payment.amount;
      
      if (invoice.status === 'paid') {
        invoice.status = 'sent';
      }
      
      await invoice.save();
    }
    
    // Delete payment
    await Payment.findByIdAndDelete(id);

    logger.info('Payment deleted', { 
      paymentId: payment._id,
      invoiceId: payment.invoice,
      amount: payment.amount,
      requestId: req.requestId 
    });

    res.json({
      data: { message: 'Payment deleted successfully' },
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error deleting payment', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  getAllPayments,
  getPaymentById,
  createPayment,
  updatePayment,
  deletePayment,
};