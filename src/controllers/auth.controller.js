const bcrypt = require('bcryptjs');
const Staff = require('../models/Staff');
const { generateToken } = require('../config/auth');
const logger = require('../config/logger');

const login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validate input
    if (!email || !password) {
      return res.status(400).json({ error: 'Email and password are required' });
    }

    // Find staff by email
    const staff = await Staff.findOne({ email: email.toLowerCase(), is_active: true });
    if (!staff) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Check password
    const isPasswordValid = await bcrypt.compare(password, staff.password);
    if (!isPasswordValid) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Generate JWT token
    const token = generateToken({
      id: staff._id,
      email: staff.email,
      role: staff.role,
      first_name: staff.first_name,
      last_name: staff.last_name,
    });

    // Remove password from response
    const staffObj = staff.toObject();
    delete staffObj.password;

    logger.info('Staff login successful', { 
      staffId: staff._id, 
      email: staff.email,
      role: staff.role,
      requestId: req.requestId 
    });

    res.json({
      data: {
        staff: staffObj,
        token,
      },
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Login error', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

const verifyToken = async (req, res) => {
  try {
    const staff = await Staff.findById(req.user.id);
    if (!staff || !staff.is_active) {
      return res.status(401).json({ error: 'Invalid token' });
    }

    // Remove password from response
    const staffObj = staff.toObject();
    delete staffObj.password;

    res.json({
      data: {
        staff: staffObj,
      },
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Token verification error', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  login,
  verifyToken,
};