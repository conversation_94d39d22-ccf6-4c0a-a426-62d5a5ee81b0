const Invoice = require('../models/Invoice');
const Patient = require('../models/Patient');
const Staff = require('../models/Staff');
const logger = require('../config/logger');

// GET /invoices - Get all invoices with pagination and search
const getAllInvoices = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || '';
    const status = req.query.status || '';
    const dateFrom = req.query.dateFrom || '';
    const dateTo = req.query.dateTo || '';
    const patientId = req.query.patientId || '';
    const sortBy = req.query.sortBy || 'invoice_date';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;

    // Ensure limit doesn't exceed 100
    const maxLimit = Math.min(limit, 100);

    // Build search query
    const searchQuery = {};
    
    if (search) {
      searchQuery.$or = [
        { invoice_number: { $regex: search, $options: 'i' } },
        { notes: { $regex: search, $options: 'i' } },
      ];
    }
    
    if (status) {
      searchQuery.status = status;
    }
    
    if (patientId) {
      searchQuery.patient = patientId;
    }
    
    if (dateFrom || dateTo) {
      searchQuery.invoice_date = {};
      if (dateFrom) {
        searchQuery.invoice_date.$gte = new Date(dateFrom);
      }
      if (dateTo) {
        searchQuery.invoice_date.$lte = new Date(dateTo);
      }
    }

    // Get invoices with pagination
    const invoices = await Invoice.find(searchQuery)
      .populate('patient', 'first_name last_name phone')
      .populate('created_by', 'first_name last_name')
      .sort({ [sortBy]: sortOrder })
      .limit(maxLimit * 1)
      .skip((page - 1) * maxLimit);

    // Get total count for pagination
    const total = await Invoice.countDocuments(searchQuery);

    logger.info('Invoices retrieved', { 
      count: invoices.length, 
      page, 
      limit: maxLimit,
      requestId: req.requestId 
    });

    res.json({
      data: invoices,
      meta: {
        page,
        limit: maxLimit,
        total,
        pages: Math.ceil(total / maxLimit),
      },
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving invoices', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// GET /invoices/:id - Get invoice by ID
const getInvoiceById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const invoice = await Invoice.findById(id)
      .populate('patient', 'first_name last_name phone email')
      .populate('created_by', 'first_name last_name');
    
    if (!invoice) {
      return res.status(404).json({ error: 'Invoice not found' });
    }

    logger.info('Invoice retrieved', { 
      invoiceId: invoice._id,
      requestId: req.requestId 
    });

    res.json({
      data: invoice,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving invoice', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// POST /invoices - Create new invoice
const createInvoice = async (req, res) => {
  try {
    const invoiceData = req.body;
    invoiceData.created_by = req.user.id;
    
    // Verify patient exists
    const patient = await Patient.findById(invoiceData.patient);
    if (!patient) {
      return res.status(400).json({ error: 'Patient not found' });
    }
    
    // Verify staff exists
    const staff = await Staff.findById(invoiceData.created_by);
    if (!staff) {
      return res.status(400).json({ error: 'Staff not found' });
    }
    
    // Calculate totals
    let subtotal = 0;
    for (const item of invoiceData.items) {
      item.total = item.quantity * item.unit_price;
      subtotal += item.total;
    }
    
    invoiceData.subtotal = subtotal;
    invoiceData.total_amount = subtotal + (invoiceData.tax || 0);
    invoiceData.balance_due = invoiceData.total_amount - invoiceData.amount_paid;
    
    // Generate invoice number if not provided
    if (!invoiceData.invoice_number) {
      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const random = Math.floor(1000 + Math.random() * 9000);
      invoiceData.invoice_number = `INV-${year}${month}${day}-${random}`;
    }
    
    // Create new invoice
    const invoice = new Invoice(invoiceData);
    await invoice.save();
    
    // Populate references
    await invoice.populate('patient', 'first_name last_name phone');
    await invoice.populate('created_by', 'first_name last_name');

    logger.info('Invoice created', { 
      invoiceId: invoice._id,
      requestId: req.requestId 
    });

    res.status(201).json({
      data: invoice,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error creating invoice', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    
    if (error.code === 11000) {
      return res.status(400).json({ error: 'Invoice with this number already exists' });
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// PUT /invoices/:id - Update invoice
const updateInvoice = async (req, res) => {
  try {
    const { id } = req.params;
    const invoiceData = req.body;
    
    // Find existing invoice
    const existingInvoice = await Invoice.findById(id);
    if (!existingInvoice) {
      return res.status(404).json({ error: 'Invoice not found' });
    }
    
    // Prevent updating if invoice is paid
    if (existingInvoice.status === 'paid') {
      return res.status(400).json({ error: 'Cannot update a paid invoice' });
    }
    
    // Recalculate totals if items are updated
    if (invoiceData.items) {
      let subtotal = 0;
      for (const item of invoiceData.items) {
        item.total = item.quantity * item.unit_price;
        subtotal += item.total;
      }
      
      invoiceData.subtotal = subtotal;
      invoiceData.total_amount = subtotal + (invoiceData.tax || 0);
      invoiceData.balance_due = invoiceData.total_amount - (invoiceData.amount_paid || existingInvoice.amount_paid);
    }
    
    const invoice = await Invoice.findByIdAndUpdate(
      id,
      invoiceData,
      { new: true, runValidators: true }
    )
      .populate('patient', 'first_name last_name phone')
      .populate('created_by', 'first_name last_name');
    
    if (!invoice) {
      return res.status(404).json({ error: 'Invoice not found' });
    }

    logger.info('Invoice updated', { 
      invoiceId: invoice._id,
      requestId: req.requestId 
    });

    res.json({
      data: invoice,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error updating invoice', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    
    if (error.code === 11000) {
      return res.status(400).json({ error: 'Invoice with this number already exists' });
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// DELETE /invoices/:id - Delete invoice
const deleteInvoice = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Find invoice
    const invoice = await Invoice.findById(id);
    if (!invoice) {
      return res.status(404).json({ error: 'Invoice not found' });
    }
    
    // Prevent deleting if invoice is paid
    if (invoice.status === 'paid') {
      return res.status(400).json({ error: 'Cannot delete a paid invoice' });
    }
    
    // Delete invoice
    await Invoice.findByIdAndDelete(id);

    logger.info('Invoice deleted', { 
      invoiceId: invoice._id,
      requestId: req.requestId 
    });

    res.json({
      data: { message: 'Invoice deleted successfully' },
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error deleting invoice', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  getAllInvoices,
  getInvoiceById,
  createInvoice,
  updateInvoice,
  deleteInvoice,
};