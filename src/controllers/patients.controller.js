const Patient = require('../models/Patient');
const logger = require('../config/logger');

// GET /patients - Get all patients with pagination and search
const getAllPatients = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || '';
    const sortBy = req.query.sortBy || 'created_at';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;

    // Ensure limit doesn't exceed 100
    const maxLimit = Math.min(limit, 100);

    // Build search query
    const searchQuery = search
      ? {
          $or: [
            { first_name: { $regex: search, $options: 'i' } },
            { last_name: { $regex: search, $options: 'i' } },
            { phone: { $regex: search, $options: 'i' } },
            { email: { $regex: search, $options: 'i' } },
          ],
        }
      : {};

    // Get patients with pagination
    const patients = await Patient.find(searchQuery)
      .sort({ [sortBy]: sortOrder })
      .limit(maxLimit * 1)
      .skip((page - 1) * maxLimit);

    // Get total count for pagination
    const total = await Patient.countDocuments(searchQuery);

    logger.info('Patients retrieved', { 
      count: patients.length, 
      page, 
      limit: maxLimit,
      requestId: req.requestId 
    });

    res.json({
      data: patients,
      meta: {
        page,
        limit: maxLimit,
        total,
        pages: Math.ceil(total / maxLimit),
      },
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving patients', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// GET /patients/:id - Get patient by ID
const getPatientById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const patient = await Patient.findById(id);
    
    if (!patient) {
      return res.status(404).json({ error: 'Patient not found' });
    }

    logger.info('Patient retrieved', { 
      patientId: patient._id,
      requestId: req.requestId 
    });

    res.json({
      data: patient,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving patient', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// POST /patients - Create new patient
const createPatient = async (req, res) => {
  try {
    const patientData = req.body;
    
    // Create new patient
    const patient = new Patient(patientData);
    await patient.save();

    logger.info('Patient created', { 
      patientId: patient._id,
      requestId: req.requestId 
    });

    res.status(201).json({
      data: patient,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error creating patient', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    
    if (error.code === 11000) {
      return res.status(400).json({ error: 'Patient with this phone number already exists' });
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// PUT /patients/:id - Update patient
const updatePatient = async (req, res) => {
  try {
    const { id } = req.params;
    const patientData = req.body;
    
    const patient = await Patient.findByIdAndUpdate(
      id,
      patientData,
      { new: true, runValidators: true }
    );
    
    if (!patient) {
      return res.status(404).json({ error: 'Patient not found' });
    }

    logger.info('Patient updated', { 
      patientId: patient._id,
      requestId: req.requestId 
    });

    res.json({
      data: patient,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error updating patient', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    
    if (error.code === 11000) {
      return res.status(400).json({ error: 'Patient with this phone number already exists' });
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// DELETE /patients/:id - Delete patient
const deletePatient = async (req, res) => {
  try {
    const { id } = req.params;
    
    const patient = await Patient.findByIdAndDelete(id);
    
    if (!patient) {
      return res.status(404).json({ error: 'Patient not found' });
    }

    logger.info('Patient deleted', { 
      patientId: patient._id,
      requestId: req.requestId 
    });

    res.json({
      data: { message: 'Patient deleted successfully' },
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error deleting patient', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  getAllPatients,
  getPatientById,
  createPatient,
  updatePatient,
  deletePatient,
};