const bcrypt = require('bcryptjs');
const Staff = require('../models/Staff');
const logger = require('../config/logger');

// GET /staff - Get all staff with pagination and search
const getAllStaff = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || '';
    const sortBy = req.query.sortBy || 'created_at';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;

    // Ensure limit doesn't exceed 100
    const maxLimit = Math.min(limit, 100);

    // Build search query
    const searchQuery = search
      ? {
          $or: [
            { first_name: { $regex: search, $options: 'i' } },
            { last_name: { $regex: search, $options: 'i' } },
            { email: { $regex: search, $options: 'i' } },
            { phone: { $regex: search, $options: 'i' } },
          ],
        }
      : {};

    // Get staff with pagination
    const staff = await Staff.find(searchQuery)
      .sort({ [sortBy]: sortOrder })
      .limit(maxLimit * 1)
      .skip((page - 1) * maxLimit);

    // Get total count for pagination
    const total = await Staff.countDocuments(searchQuery);

    logger.info('Staff retrieved', { 
      count: staff.length, 
      page, 
      limit: maxLimit,
      requestId: req.requestId 
    });

    res.json({
      data: staff,
      meta: {
        page,
        limit: maxLimit,
        total,
        pages: Math.ceil(total / maxLimit),
      },
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving staff', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// GET /staff/:id - Get staff by ID
const getStaffById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const staff = await Staff.findById(id);
    
    if (!staff) {
      return res.status(404).json({ error: 'Staff not found' });
    }

    logger.info('Staff retrieved', { 
      staffId: staff._id,
      requestId: req.requestId 
    });

    res.json({
      data: staff,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving staff', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// POST /staff - Create new staff
const createStaff = async (req, res) => {
  try {
    const staffData = req.body;
    
    // Hash password if provided
    if (staffData.password) {
      staffData.password = await bcrypt.hash(staffData.password, 10);
    }
    
    // Create new staff
    const staff = new Staff(staffData);
    await staff.save();

    logger.info('Staff created', { 
      staffId: staff._id,
      requestId: req.requestId 
    });

    res.status(201).json({
      data: staff,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error creating staff', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    
    if (error.code === 11000) {
      return res.status(400).json({ error: 'Staff with this email already exists' });
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// PUT /staff/:id - Update staff
const updateStaff = async (req, res) => {
  try {
    const { id } = req.params;
    const staffData = req.body;
    
    // Hash password if provided
    if (staffData.password) {
      staffData.password = await bcrypt.hash(staffData.password, 10);
    }
    
    const staff = await Staff.findByIdAndUpdate(
      id,
      staffData,
      { new: true, runValidators: true }
    );
    
    if (!staff) {
      return res.status(404).json({ error: 'Staff not found' });
    }

    logger.info('Staff updated', { 
      staffId: staff._id,
      requestId: req.requestId 
    });

    res.json({
      data: staff,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error updating staff', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    
    if (error.code === 11000) {
      return res.status(400).json({ error: 'Staff with this email already exists' });
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// DELETE /staff/:id - Delete staff
const deleteStaff = async (req, res) => {
  try {
    const { id } = req.params;
    
    const staff = await Staff.findByIdAndDelete(id);
    
    if (!staff) {
      return res.status(404).json({ error: 'Staff not found' });
    }

    logger.info('Staff deleted', { 
      staffId: staff._id,
      requestId: req.requestId 
    });

    res.json({
      data: { message: 'Staff deleted successfully' },
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error deleting staff', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  getAllStaff,
  getStaffById,
  createStaff,
  updateStaff,
  deleteStaff,
};