const axios = require('axios');
const crypto = require('crypto');
const MpesaReceipt = require('../models/MpesaReceipt');
const Invoice = require('../models/Invoice');
const Payment = require('../models/Payment');
const logger = require('../config/logger');

// Function to get M-Pesa access token
const getMpesaAccessToken = async () => {
  const consumerKey = process.env.MPESA_CONSUMER_KEY;
  const consumerSecret = process.env.MPESA_CONSUMER_SECRET;
  const url = process.env.MPESA_ENV === 'sandbox' 
    ? 'https://sandbox.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials'
    : 'https://api.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials';

  const auth = Buffer.from(`${consumerKey}:${consumerSecret}`).toString('base64');

  try {
    const response = await axios.get(url, {
      headers: {
        Authorization: `Basic ${auth}`,
      },
    });

    return response.data.access_token;
  } catch (error) {
    logger.error('Error getting M-Pesa access token', { 
      error: error.message, 
      stack: error.stack 
    });
    throw new Error('Failed to get M-Pesa access token');
  }
};

// Function to initiate STK Push
const initiateSTKPush = async (phone, amount, invoiceId) => {
  const accessToken = await getMpesaAccessToken();
  const url = process.env.MPESA_ENV === 'sandbox'
    ? 'https://sandbox.safaricom.co.ke/mpesa/stkpush/v1/processrequest'
    : 'https://api.safaricom.co.ke/mpesa/stkpush/v1/processrequest';

  // Format phone number
  let formattedPhone = phone;
  if (phone.startsWith('0')) {
    formattedPhone = `254${phone.substring(1)}`;
  } else if (phone.startsWith('+')) {
    formattedPhone = phone.substring(1);
  }

  // Generate password
  const timestamp = new Date().toISOString().replace(/[-:T]/g, '').slice(0, 14);
  const password = Buffer.from(`${process.env.MPESA_SHORTCODE}${process.env.MPESA_PASSKEY}${timestamp}`).toString('base64');

  const data = {
    BusinessShortCode: process.env.MPESA_SHORTCODE,
    Password: password,
    Timestamp: timestamp,
    TransactionType: 'CustomerPayBillOnline',
    Amount: amount,
    PartyA: formattedPhone,
    PartyB: process.env.MPESA_SHORTCODE,
    PhoneNumber: formattedPhone,
    CallBackURL: process.env.MPESA_CALLBACK_URL,
    AccountReference: `INV-${invoiceId.substring(0, 8)}`,
    TransactionDesc: `Payment for Invoice ${invoiceId.substring(0, 8)}`,
  };

  try {
    const response = await axios.post(url, data, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    return response.data;
  } catch (error) {
    logger.error('Error initiating STK Push', { 
      error: error.message, 
      stack: error.stack,
      data 
    });
    throw new Error('Failed to initiate STK Push');
  }
};

// POST /payments/mpesa/initiate - Initiate M-Pesa STK Push
const initiateMpesaPayment = async (req, res) => {
  try {
    const { invoice_id, phone_number, amount } = req.body;
    const userId = req.user.id;

    // Validate input
    if (!invoice_id || !phone_number || !amount) {
      return res.status(400).json({ error: 'Invoice ID, phone number, and amount are required' });
    }

    // Find invoice
    const invoice = await Invoice.findById(invoice_id);
    if (!invoice) {
      return res.status(404).json({ error: 'Invoice not found' });
    }

    // Check if amount is valid
    if (amount > invoice.balance_due) {
      return res.status(400).json({ error: 'Amount cannot exceed balance due' });
    }

    // Initiate STK Push
    const stkResponse = await initiateSTKPush(phone_number, amount, invoice_id);

    // Create pending M-Pesa receipt record
    const mpesaReceipt = new MpesaReceipt({
      invoice: invoice_id,
      patient: invoice.patient,
      transaction_id: `txn_${Date.now()}`,
      transaction_date: new Date(),
      phone_number: phone_number,
      amount: amount,
      checkout_request_id: stkResponse.CheckoutRequestID,
      merchant_request_id: stkResponse.MerchantRequestID,
      status: 'pending',
      raw_payload: stkResponse,
    });

    await mpesaReceipt.save();

    logger.info('M-Pesa payment initiated', { 
      invoiceId: invoice_id,
      phoneNumber: phone_number,
      amount: amount,
      checkoutRequestId: stkResponse.CheckoutRequestID,
      requestId: req.requestId 
    });

    res.json({
      data: {
        checkoutRequestID: stkResponse.CheckoutRequestID,
      },
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error initiating M-Pesa payment', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// POST /payments/mpesa/callback - Handle M-Pesa callback
const handleMpesaCallback = async (req, res) => {
  try {
    const payload = req.body;
    logger.info('M-Pesa callback received', { payload });

    // Extract relevant data from callback
    const { Body } = payload;
    if (!Body) {
      return res.status(400).json({ error: 'Invalid callback payload' });
    }

    const { stkCallback } = Body;
    if (!stkCallback) {
      return res.status(400).json({ error: 'Invalid STK callback' });
    }

    const { 
      MerchantRequestID, 
      CheckoutRequestID, 
      ResultCode, 
      ResultDesc, 
      CallbackMetadata 
    } = stkCallback;

    // Find the M-Pesa receipt record
    const mpesaReceipt = await MpesaReceipt.findOne({ checkout_request_id: CheckoutRequestID });
    if (!mpesaReceipt) {
      logger.warn('M-Pesa receipt not found for callback', { CheckoutRequestID });
      return res.status(404).json({ error: 'M-Pesa receipt not found' });
    }

    // Update M-Pesa receipt status
    mpesaReceipt.result_code = ResultCode;
    mpesaReceipt.result_desc = ResultDesc;
    
    let mpesaReceiptNumber = null;
    if (CallbackMetadata && CallbackMetadata.Item) {
      CallbackMetadata.Item.forEach(item => {
        if (item.Name === 'MpesaReceiptNumber') {
          mpesaReceiptNumber = item.Value;
          mpesaReceipt.mpesa_receipt_number = item.Value;
        }
      });
    }

    // Update status based on result code
    if (ResultCode === 0) {
      mpesaReceipt.status = 'completed';
      
      // If we have an associated invoice, update it
      if (mpesaReceipt.invoice) {
        const invoice = await Invoice.findById(mpesaReceipt.invoice);
        if (invoice) {
          // Create payment record
          const payment = new Payment({
            invoice: mpesaReceipt.invoice,
            patient: mpesaReceipt.patient,
            amount: mpesaReceipt.amount,
            payment_method: 'mpesa',
            payment_date: new Date(),
            reference_number: mpesaReceiptNumber,
            notes: `M-Pesa payment for invoice ${invoice.invoice_number}`,
            created_by: invoice.created_by,
          });
          
          await payment.save();
          
          // Update invoice status and amounts
          invoice.amount_paid += mpesaReceipt.amount;
          invoice.balance_due = invoice.total_amount - invoice.amount_paid;
          
          if (invoice.balance_due <= 0) {
            invoice.status = 'paid';
          } else if (invoice.status === 'draft') {
            invoice.status = 'sent';
          }
          
          await invoice.save();
        }
      }
    } else {
      mpesaReceipt.status = 'failed';
    }

    await mpesaReceipt.save();

    logger.info('M-Pesa callback processed', { 
      checkoutRequestId: CheckoutRequestID,
      resultCode: ResultCode,
      resultDesc: ResultDesc,
      mpesaReceiptNumber: mpesaReceiptNumber
    });

    res.json({
      ResultCode: 0,
      ResultDesc: 'Success',
    });
  } catch (error) {
    logger.error('Error handling M-Pesa callback', { 
      error: error.message, 
      stack: error.stack 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  initiateMpesaPayment,
  handleMpesaCallback,
};