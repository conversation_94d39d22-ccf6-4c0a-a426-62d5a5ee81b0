const Admission = require('../models/Admission');
const Patient = require('../models/Patient');
const Staff = require('../models/Staff');
const Bed = require('../models/Bed');
const Department = require('../models/Department');
const logger = require('../config/logger');

// GET /admissions - Get all admissions with pagination and search
const getAllAdmissions = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || '';
    const status = req.query.status || '';
    const dateFrom = req.query.dateFrom || '';
    const dateTo = req.query.dateTo || '';
    const sortBy = req.query.sortBy || 'admission_date';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;

    // Ensure limit doesn't exceed 100
    const maxLimit = Math.min(limit, 100);

    // Build search query
    const searchQuery = {};
    
    if (search) {
      searchQuery.$or = [
        { reason: { $regex: search, $options: 'i' } },
        { diagnosis: { $regex: search, $options: 'i' } },
        { notes: { $regex: search, $options: 'i' } },
      ];
    }
    
    if (status) {
      searchQuery.status = status;
    }
    
    if (dateFrom || dateTo) {
      searchQuery.admission_date = {};
      if (dateFrom) {
        searchQuery.admission_date.$gte = new Date(dateFrom);
      }
      if (dateTo) {
        searchQuery.admission_date.$lte = new Date(dateTo);
      }
    }

    // Get admissions with pagination
    const admissions = await Admission.find(searchQuery)
      .populate('patient', 'first_name last_name phone')
      .populate('admitting_doctor', 'first_name last_name')
      .populate('bed', 'bed_number room_number ward status')
      .populate('department', 'name')
      .populate('created_by', 'first_name last_name')
      .sort({ [sortBy]: sortOrder })
      .limit(maxLimit * 1)
      .skip((page - 1) * maxLimit);

    // Get total count for pagination
    const total = await Admission.countDocuments(searchQuery);

    logger.info('Admissions retrieved', { 
      count: admissions.length, 
      page, 
      limit: maxLimit,
      requestId: req.requestId 
    });

    res.json({
      data: admissions,
      meta: {
        page,
        limit: maxLimit,
        total,
        pages: Math.ceil(total / maxLimit),
      },
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving admissions', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// GET /admissions/:id - Get admission by ID
const getAdmissionById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const admission = await Admission.findById(id)
      .populate('patient', 'first_name last_name phone email')
      .populate('admitting_doctor', 'first_name last_name')
      .populate('bed', 'bed_number room_number ward status')
      .populate('department', 'name')
      .populate('created_by', 'first_name last_name');
    
    if (!admission) {
      return res.status(404).json({ error: 'Admission not found' });
    }

    logger.info('Admission retrieved', { 
      admissionId: admission._id,
      requestId: req.requestId 
    });

    res.json({
      data: admission,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving admission', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// POST /admissions - Create new admission
const createAdmission = async (req, res) => {
  try {
    const admissionData = req.body;
    admissionData.created_by = req.user.id;
    
    // Verify patient exists
    const patient = await Patient.findById(admissionData.patient);
    if (!patient) {
      return res.status(400).json({ error: 'Patient not found' });
    }
    
    // Verify admitting doctor exists
    const doctor = await Staff.findById(admissionData.admitting_doctor);
    if (!doctor) {
      return res.status(400).json({ error: 'Doctor not found' });
    }
    
    // Verify bed exists and is available
    const bed = await Bed.findById(admissionData.bed);
    if (!bed) {
      return res.status(400).json({ error: 'Bed not found' });
    }
    
    if (bed.status !== 'available') {
      return res.status(400).json({ error: 'Bed is not available' });
    }
    
    // Verify department exists
    const department = await Department.findById(admissionData.department);
    if (!department) {
      return res.status(400).json({ error: 'Department not found' });
    }
    
    // Set bed status to occupied
    bed.status = 'occupied';
    await bed.save();
    
    // Create new admission
    const admission = new Admission(admissionData);
    await admission.save();
    
    // Populate references
    await admission.populate('patient', 'first_name last_name phone');
    await admission.populate('admitting_doctor', 'first_name last_name');
    await admission.populate('bed', 'bed_number room_number ward status');
    await admission.populate('department', 'name');
    await admission.populate('created_by', 'first_name last_name');

    logger.info('Admission created', { 
      admissionId: admission._id,
      bedId: bed._id,
      requestId: req.requestId 
    });

    res.status(201).json({
      data: admission,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error creating admission', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// PUT /admissions/:id/discharge - Discharge patient
const dischargePatient = async (req, res) => {
  try {
    const { id } = req.params;
    const { discharge_date, diagnosis, notes } = req.body;
    
    // Find admission
    const admission = await Admission.findById(id);
    if (!admission) {
      return res.status(404).json({ error: 'Admission not found' });
    }
    
    if (admission.status === 'discharged') {
      return res.status(400).json({ error: 'Patient already discharged' });
    }
    
    // Update admission
    admission.discharge_date = discharge_date || new Date();
    admission.diagnosis = diagnosis;
    admission.notes = notes;
    admission.status = 'discharged';
    await admission.save();
    
    // Free up the bed
    const bed = await Bed.findById(admission.bed);
    if (bed) {
      bed.status = 'available';
      await bed.save();
    }
    
    // Populate references
    await admission.populate('patient', 'first_name last_name phone');
    await admission.populate('admitting_doctor', 'first_name last_name');
    await admission.populate('bed', 'bed_number room_number ward status');
    await admission.populate('department', 'name');
    await admission.populate('created_by', 'first_name last_name');

    logger.info('Patient discharged', { 
      admissionId: admission._id,
      bedId: bed._id,
      requestId: req.requestId 
    });

    res.json({
      data: admission,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error discharging patient', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// PUT /admissions/:id - Update admission
const updateAdmission = async (req, res) => {
  try {
    const { id } = req.params;
    const admissionData = req.body;
    
    const admission = await Admission.findByIdAndUpdate(
      id,
      admissionData,
      { new: true, runValidators: true }
    )
      .populate('patient', 'first_name last_name phone')
      .populate('admitting_doctor', 'first_name last_name')
      .populate('bed', 'bed_number room_number ward status')
      .populate('department', 'name')
      .populate('created_by', 'first_name last_name');
    
    if (!admission) {
      return res.status(404).json({ error: 'Admission not found' });
    }

    logger.info('Admission updated', { 
      admissionId: admission._id,
      requestId: req.requestId 
    });

    res.json({
      data: admission,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error updating admission', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// DELETE /admissions/:id - Delete admission
const deleteAdmission = async (req, res) => {
  try {
    const { id } = req.params;
    
    const admission = await Admission.findByIdAndDelete(id);
    
    if (!admission) {
      return res.status(404).json({ error: 'Admission not found' });
    }

    logger.info('Admission deleted', { 
      admissionId: admission._id,
      requestId: req.requestId 
    });

    res.json({
      data: { message: 'Admission deleted successfully' },
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error deleting admission', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  getAllAdmissions,
  getAdmissionById,
  createAdmission,
  dischargePatient,
  updateAdmission,
  deleteAdmission,
};