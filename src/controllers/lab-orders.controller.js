const LabOrder = require('../models/LabOrder');
const Patient = require('../models/Patient');
const Staff = require('../models/Staff');
const logger = require('../config/logger');

// GET /lab-orders - Get all lab orders with pagination and search
const getAllLabOrders = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || '';
    const status = req.query.status || '';
    const dateFrom = req.query.dateFrom || '';
    const dateTo = req.query.dateTo || '';
    const patientId = req.query.patientId || '';
    const sortBy = req.query.sortBy || 'order_date';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;

    // Ensure limit doesn't exceed 100
    const maxLimit = Math.min(limit, 100);

    // Build search query
    const searchQuery = {};
    
    if (search) {
      searchQuery.$or = [
        { test_name: { $regex: search, $options: 'i' } },
        { test_description: { $regex: search, $options: 'i' } },
        { results: { $regex: search, $options: 'i' } },
      ];
    }
    
    if (status) {
      searchQuery.status = status;
    }
    
    if (patientId) {
      searchQuery.patient = patientId;
    }
    
    if (dateFrom || dateTo) {
      searchQuery.order_date = {};
      if (dateFrom) {
        searchQuery.order_date.$gte = new Date(dateFrom);
      }
      if (dateTo) {
        searchQuery.order_date.$lte = new Date(dateTo);
      }
    }

    // Get lab orders with pagination
    const labOrders = await LabOrder.find(searchQuery)
      .populate('patient', 'first_name last_name phone')
      .populate('doctor', 'first_name last_name')
      .populate('created_by', 'first_name last_name')
      .sort({ [sortBy]: sortOrder })
      .limit(maxLimit * 1)
      .skip((page - 1) * maxLimit);

    // Get total count for pagination
    const total = await LabOrder.countDocuments(searchQuery);

    logger.info('Lab orders retrieved', { 
      count: labOrders.length, 
      page, 
      limit: maxLimit,
      requestId: req.requestId 
    });

    res.json({
      data: labOrders,
      meta: {
        page,
        limit: maxLimit,
        total,
        pages: Math.ceil(total / maxLimit),
      },
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving lab orders', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// GET /lab-orders/:id - Get lab order by ID
const getLabOrderById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const labOrder = await LabOrder.findById(id)
      .populate('patient', 'first_name last_name phone email')
      .populate('doctor', 'first_name last_name')
      .populate('created_by', 'first_name last_name');
    
    if (!labOrder) {
      return res.status(404).json({ error: 'Lab order not found' });
    }

    logger.info('Lab order retrieved', { 
      labOrderId: labOrder._id,
      requestId: req.requestId 
    });

    res.json({
      data: labOrder,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving lab order', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// POST /lab-orders - Create new lab order
const createLabOrder = async (req, res) => {
  try {
    const labOrderData = req.body;
    labOrderData.created_by = req.user.id;
    
    // Verify patient exists
    const patient = await Patient.findById(labOrderData.patient);
    if (!patient) {
      return res.status(400).json({ error: 'Patient not found' });
    }
    
    // Verify doctor exists
    const doctor = await Staff.findById(labOrderData.doctor);
    if (!doctor) {
      return res.status(400).json({ error: 'Doctor not found' });
    }
    
    // Verify staff exists
    const staff = await Staff.findById(labOrderData.created_by);
    if (!staff) {
      return res.status(400).json({ error: 'Staff not found' });
    }
    
    // Create new lab order
    const labOrder = new LabOrder(labOrderData);
    await labOrder.save();
    
    // Populate references
    await labOrder.populate('patient', 'first_name last_name phone');
    await labOrder.populate('doctor', 'first_name last_name');
    await labOrder.populate('created_by', 'first_name last_name');

    logger.info('Lab order created', { 
      labOrderId: labOrder._id,
      requestId: req.requestId 
    });

    res.status(201).json({
      data: labOrder,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error creating lab order', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// PUT /lab-orders/:id - Update lab order (add results)
const updateLabOrder = async (req, res) => {
  try {
    const { id } = req.params;
    const labOrderData = req.body;
    
    // Find existing lab order
    const existingLabOrder = await LabOrder.findById(id);
    if (!existingLabOrder) {
      return res.status(404).json({ error: 'Lab order not found' });
    }
    
    // Update lab order
    const labOrder = await LabOrder.findByIdAndUpdate(
      id,
      labOrderData,
      { new: true, runValidators: true }
    )
      .populate('patient', 'first_name last_name phone')
      .populate('doctor', 'first_name last_name')
      .populate('created_by', 'first_name last_name');
    
    if (!labOrder) {
      return res.status(404).json({ error: 'Lab order not found' });
    }

    logger.info('Lab order updated', { 
      labOrderId: labOrder._id,
      requestId: req.requestId 
    });

    res.json({
      data: labOrder,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error updating lab order', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// DELETE /lab-orders/:id - Delete lab order
const deleteLabOrder = async (req, res) => {
  try {
    const { id } = req.params;
    
    const labOrder = await LabOrder.findByIdAndDelete(id);
    
    if (!labOrder) {
      return res.status(404).json({ error: 'Lab order not found' });
    }

    logger.info('Lab order deleted', { 
      labOrderId: labOrder._id,
      requestId: req.requestId 
    });

    res.json({
      data: { message: 'Lab order deleted successfully' },
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error deleting lab order', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  getAllLabOrders,
  getLabOrderById,
  createLabOrder,
  updateLabOrder,
  deleteLabOrder,
};