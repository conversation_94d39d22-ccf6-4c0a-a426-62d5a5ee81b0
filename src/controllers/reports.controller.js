const ReportDaily = require('../models/ReportDaily');
const Patient = require('../models/Patient');
const Appointment = require('../models/Appointment');
const Admission = require('../models/Admission');
const Bed = require('../models/Bed');
const Invoice = require('../models/Invoice');
const Payment = require('../models/Payment');
const MpesaReceipt = require('../models/MpesaReceipt');
const logger = require('../config/logger');
const { createObjectCsvWriter } = require('csv-writer');

// GET /reports/daily - Get daily reports with pagination and search
const getDailyReports = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const dateFrom = req.query.dateFrom || '';
    const dateTo = req.query.dateTo || '';
    const sortBy = req.query.sortBy || 'date';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;

    // Ensure limit doesn't exceed 100
    const maxLimit = Math.min(limit, 100);

    // Build search query
    const searchQuery = {};
    
    if (dateFrom || dateTo) {
      searchQuery.date = {};
      if (dateFrom) {
        searchQuery.date.$gte = new Date(dateFrom);
      }
      if (dateTo) {
        searchQuery.date.$lte = new Date(dateTo);
      }
    }

    // Get daily reports with pagination
    const reports = await ReportDaily.find(searchQuery)
      .sort({ [sortBy]: sortOrder })
      .limit(maxLimit * 1)
      .skip((page - 1) * maxLimit);

    // Get total count for pagination
    const total = await ReportDaily.countDocuments(searchQuery);

    logger.info('Daily reports retrieved', { 
      count: reports.length, 
      page, 
      limit: maxLimit,
      requestId: req.requestId 
    });

    res.json({
      data: reports,
      meta: {
        page,
        limit: maxLimit,
        total,
        pages: Math.ceil(total / maxLimit),
      },
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving daily reports', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// GET /reports/daily/:id - Get daily report by ID
const getDailyReportById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const report = await ReportDaily.findById(id);
    
    if (!report) {
      return res.status(404).json({ error: 'Daily report not found' });
    }

    logger.info('Daily report retrieved', { 
      reportId: report._id,
      requestId: req.requestId 
    });

    res.json({
      data: report,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving daily report', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// GET /reports/daily/kpis - Get last 7 days KPIs
const getLast7DaysKPIs = async (req, res) => {
  try {
    const today = new Date();
    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(today.getDate() - 7);
    
    const reports = await ReportDaily.find({
      date: {
        $gte: sevenDaysAgo,
        $lte: today,
      },
    }).sort({ date: -1 });

    logger.info('Last 7 days KPIs retrieved', { 
      count: reports.length,
      requestId: req.requestId 
    });

    res.json({
      data: reports,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving last 7 days KPIs', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// GET /reports/daily/export - Export daily reports as CSV
const exportDailyReports = async (req, res) => {
  try {
    const dateFrom = req.query.dateFrom || '';
    const dateTo = req.query.dateTo || '';
    
    // Build search query
    const searchQuery = {};
    
    if (dateFrom || dateTo) {
      searchQuery.date = {};
      if (dateFrom) {
        searchQuery.date.$gte = new Date(dateFrom);
      }
      if (dateTo) {
        searchQuery.date.$lte = new Date(dateTo);
      }
    }
    
    // Get reports
    const reports = await ReportDaily.find(searchQuery).sort({ date: -1 });
    
    // Set response headers for CSV download
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename=daily-reports.csv');
    
    // Create CSV writer
    const csvWriter = createObjectCsvWriter({
      path: res,
      header: [
        { id: 'date', title: 'Date' },
        { id: 'new_patients', title: 'New Patients' },
        { id: 'appointments_total', title: 'Total Appointments' },
        { id: 'appointments_completed', title: 'Completed Appointments' },
        { id: 'admissions_open', title: 'Open Admissions' },
        { id: 'beds_available', title: 'Available Beds' },
        { id: 'revenue', title: 'Revenue' },
        { id: 'accounts_receivable_balance', title: 'AR Balance' },
        { id: 'mpesa_total_confirmed', title: 'M-Pesa Confirmed' },
      ],
    });
    
    // Format data for CSV
    const csvData = reports.map(report => ({
      date: report.date.toISOString().split('T')[0],
      new_patients: report.new_patients,
      appointments_total: report.appointments_total,
      appointments_completed: report.appointments_completed,
      admissions_open: report.admissions_open,
      beds_available: report.beds_available,
      revenue: report.revenue,
      accounts_receivable_balance: report.accounts_receivable_balance,
      mpesa_total_confirmed: report.mpesa_total_confirmed,
    }));
    
    // Write CSV data
    await csvWriter.writeRecords(csvData);
    
    logger.info('Daily reports exported as CSV', { 
      count: reports.length,
      requestId: req.requestId 
    });
  } catch (error) {
    logger.error('Error exporting daily reports as CSV', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// POST /reports/daily/generate - Generate daily report (internal use)
const generateDailyReport = async (req, res) => {
  try {
    const reportDate = req.body.date ? new Date(req.body.date) : new Date();
    reportDate.setHours(0, 0, 0, 0); // Set to start of day
    
    // Check if report already exists for this date
    const existingReport = await ReportDaily.findOne({ date: reportDate });
    if (existingReport) {
      return res.status(400).json({ error: 'Report already exists for this date' });
    }
    
    // Calculate KPIs for the day
    const kpis = await calculateDailyKPIs(reportDate);
    
    // Create new report
    const report = new ReportDaily({
      date: reportDate,
      ...kpis,
    });
    
    await report.save();

    logger.info('Daily report generated', { 
      reportId: report._id,
      date: reportDate,
      requestId: req.requestId 
    });

    res.status(201).json({
      data: report,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error generating daily report', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Helper function to calculate daily KPIs
const calculateDailyKPIs = async (date) => {
  try {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);
    
    // New patients count
    const newPatients = await Patient.countDocuments({
      created_at: {
        $gte: startOfDay,
        $lte: endOfDay,
      },
    });
    
    // Appointments count
    const appointmentsTotal = await Appointment.countDocuments({
      appointment_date: {
        $gte: startOfDay,
        $lte: endOfDay,
      },
    });
    
    // Completed appointments count
    const appointmentsCompleted = await Appointment.countDocuments({
      appointment_date: {
        $gte: startOfDay,
        $lte: endOfDay,
      },
      status: 'completed',
    });
    
    // Open admissions count
    const admissionsOpen = await Admission.countDocuments({
      admission_date: {
        $lte: endOfDay,
      },
      $or: [
        { discharge_date: { $exists: false } },
        { discharge_date: { $gt: endOfDay } },
      ],
    });
    
    // Available beds count
    const bedsAvailable = await Bed.countDocuments({
      status: 'available',
      is_active: true,
    });
    
    // Revenue (sum of payments for the day)
    const payments = await Payment.aggregate([
      {
        $match: {
          payment_date: {
            $gte: startOfDay,
            $lte: endOfDay,
          },
        },
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$amount' },
        },
      },
    ]);
    
    const revenue = payments.length > 0 ? payments[0].total : 0;
    
    // Accounts receivable balance (sum of unpaid invoice balances)
    const invoices = await Invoice.aggregate([
      {
        $match: {
          status: { $ne: 'paid' },
        },
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$balance_due' },
        },
      },
    ]);
    
    const accountsReceivableBalance = invoices.length > 0 ? invoices[0].total : 0;
    
    // M-Pesa confirmed payments total
    const mpesaPayments = await MpesaReceipt.aggregate([
      {
        $match: {
          transaction_date: {
            $gte: startOfDay,
            $lte: endOfDay,
          },
          status: 'completed',
        },
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$amount' },
        },
      },
    ]);
    
    const mpesaTotalConfirmed = mpesaPayments.length > 0 ? mpesaPayments[0].total : 0;
    
    return {
      new_patients: newPatients,
      appointments_total: appointmentsTotal,
      appointments_completed: appointmentsCompleted,
      admissions_open: admissionsOpen,
      beds_available: bedsAvailable,
      revenue: revenue,
      accounts_receivable_balance: accountsReceivableBalance,
      mpesa_total_confirmed: mpesaTotalConfirmed,
    };
  } catch (error) {
    logger.error('Error calculating daily KPIs', { 
      error: error.message, 
      stack: error.stack 
    });
    throw error;
  }
};

module.exports = {
  getDailyReports,
  getDailyReportById,
  getLast7DaysKPIs,
  exportDailyReports,
  generateDailyReport,
};