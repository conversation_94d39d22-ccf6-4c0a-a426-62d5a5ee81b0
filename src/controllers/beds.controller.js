const Bed = require('../models/Bed');
const Department = require('../models/Department');
const logger = require('../config/logger');

// GET /beds - Get all beds with pagination and search
const getAllBeds = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || '';
    const status = req.query.status || '';
    const department = req.query.department || '';
    const sortBy = req.query.sortBy || 'bed_number';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;

    // Ensure limit doesn't exceed 100
    const maxLimit = Math.min(limit, 100);

    // Build search query
    const searchQuery = {};
    
    if (search) {
      searchQuery.$or = [
        { bed_number: { $regex: search, $options: 'i' } },
        { room_number: { $regex: search, $options: 'i' } },
        { ward: { $regex: search, $options: 'i' } },
      ];
    }
    
    if (status) {
      searchQuery.status = status;
    }
    
    if (department) {
      searchQuery.department = department;
    }

    // Get beds with pagination
    const beds = await Bed.find(searchQuery)
      .populate('department', 'name')
      .sort({ [sortBy]: sortOrder })
      .limit(maxLimit * 1)
      .skip((page - 1) * maxLimit);

    // Get total count for pagination
    const total = await Bed.countDocuments(searchQuery);

    logger.info('Beds retrieved', { 
      count: beds.length, 
      page, 
      limit: maxLimit,
      requestId: req.requestId 
    });

    res.json({
      data: beds,
      meta: {
        page,
        limit: maxLimit,
        total,
        pages: Math.ceil(total / maxLimit),
      },
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving beds', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// GET /beds/:id - Get bed by ID
const getBedById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const bed = await Bed.findById(id)
      .populate('department', 'name');
    
    if (!bed) {
      return res.status(404).json({ error: 'Bed not found' });
    }

    logger.info('Bed retrieved', { 
      bedId: bed._id,
      requestId: req.requestId 
    });

    res.json({
      data: bed,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving bed', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// GET /beds/available - Get available beds
const getAvailableBeds = async (req, res) => {
  try {
    const beds = await Bed.find({ 
      status: 'available',
      is_active: true
    })
      .populate('department', 'name');

    logger.info('Available beds retrieved', { 
      count: beds.length,
      requestId: req.requestId 
    });

    res.json({
      data: beds,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error retrieving available beds', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

// POST /beds - Create new bed
const createBed = async (req, res) => {
  try {
    const bedData = req.body;
    
    // Check if bed number already exists
    const existingBed = await Bed.findOne({ bed_number: bedData.bed_number });
    if (existingBed) {
      return res.status(400).json({ error: 'Bed with this number already exists' });
    }
    
    // Verify department exists
    const department = await Department.findById(bedData.department);
    if (!department) {
      return res.status(400).json({ error: 'Department not found' });
    }
    
    // Create new bed
    const bed = new Bed(bedData);
    await bed.save();
    
    // Populate references
    await bed.populate('department', 'name');

    logger.info('Bed created', { 
      bedId: bed._id,
      requestId: req.requestId 
    });

    res.status(201).json({
      data: bed,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error creating bed', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// PUT /beds/:id - Update bed
const updateBed = async (req, res) => {
  try {
    const { id } = req.params;
    const bedData = req.body;
    
    // Verify department exists if being updated
    if (bedData.department) {
      const department = await Department.findById(bedData.department);
      if (!department) {
        return res.status(400).json({ error: 'Department not found' });
      }
    }
    
    const bed = await Bed.findByIdAndUpdate(
      id,
      bedData,
      { new: true, runValidators: true }
    )
      .populate('department', 'name');
    
    if (!bed) {
      return res.status(404).json({ error: 'Bed not found' });
    }

    logger.info('Bed updated', { 
      bedId: bed._id,
      requestId: req.requestId 
    });

    res.json({
      data: bed,
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error updating bed', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }
};

// DELETE /beds/:id - Delete bed
const deleteBed = async (req, res) => {
  try {
    const { id } = req.params;
    
    const bed = await Bed.findByIdAndDelete(id);
    
    if (!bed) {
      return res.status(404).json({ error: 'Bed not found' });
    }

    logger.info('Bed deleted', { 
      bedId: bed._id,
      requestId: req.requestId 
    });

    res.json({
      data: { message: 'Bed deleted successfully' },
      meta: {},
      error: null,
    });
  } catch (error) {
    logger.error('Error deleting bed', { 
      error: error.message, 
      stack: error.stack,
      requestId: req.requestId 
    });
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  getAllBeds,
  getBedById,
  getAvailableBeds,
  createBed,
  updateBed,
  deleteBed,
};