const mongoose = require('mongoose');

const mpesaReceiptSchema = new mongoose.Schema({
  invoice: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Invoice',
    required: false,
  },
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: false,
  },
  transaction_id: {
    type: String,
    required: true,
    unique: true,
  },
  transaction_date: {
    type: Date,
    required: true,
  },
  phone_number: {
    type: String,
    required: true,
  },
  amount: {
    type: Number,
    required: true,
  },
  checkout_request_id: {
    type: String,
    required: false,
  },
  merchant_request_id: {
    type: String,
    required: false,
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed', 'reversed'],
    default: 'pending',
  },
  result_code: {
    type: Number,
    required: false,
  },
  result_desc: {
    type: String,
    required: false,
  },
  mpesa_receipt_number: {
    type: String,
    required: false,
  },
  raw_payload: {
    type: mongoose.Schema.Types.Mixed,
    required: false,
  },
  created_at: {
    type: Date,
    default: Date.now,
  },
  updated_at: {
    type: Date,
    default: Date.now,
  },
});

mpesaReceiptSchema.pre('save', function (next) {
  this.updated_at = Date.now();
  next();
});

const MpesaReceipt = mongoose.model('MpesaReceipt', mpesaReceiptSchema);

module.exports = MpesaReceipt;