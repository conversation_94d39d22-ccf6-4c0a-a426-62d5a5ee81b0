const mongoose = require('mongoose');

const admissionSchema = new mongoose.Schema({
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: true,
  },
  admission_date: {
    type: Date,
    required: true,
  },
  discharge_date: {
    type: Date,
    required: false,
  },
  admitting_doctor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Staff',
    required: true,
  },
  bed: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Bed',
    required: true,
  },
  department: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Department',
    required: true,
  },
  reason: {
    type: String,
    required: true,
  },
  diagnosis: {
    type: String,
    required: false,
  },
  status: {
    type: String,
    enum: ['admitted', 'discharged', 'transferred'],
    default: 'admitted',
  },
  notes: {
    type: String,
    required: false,
  },
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Staff',
    required: true,
  },
  created_at: {
    type: Date,
    default: Date.now,
  },
  updated_at: {
    type: Date,
    default: Date.now,
  },
});

admissionSchema.pre('save', function (next) {
  this.updated_at = Date.now();
  next();
});

const Admission = mongoose.model('Admission', admissionSchema);

module.exports = Admission;