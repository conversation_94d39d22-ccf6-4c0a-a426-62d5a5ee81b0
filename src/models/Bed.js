const mongoose = require('mongoose');

const bedSchema = new mongoose.Schema({
  bed_number: {
    type: String,
    required: true,
    unique: true,
  },
  room_number: {
    type: String,
    required: true,
  },
  ward: {
    type: String,
    required: true,
  },
  department: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Department',
    required: true,
  },
  status: {
    type: String,
    enum: ['available', 'occupied', 'maintenance', 'reserved'],
    default: 'available',
  },
  is_active: {
    type: Boolean,
    default: true,
  },
  created_at: {
    type: Date,
    default: Date.now,
  },
  updated_at: {
    type: Date,
    default: Date.now,
  },
});

bedSchema.pre('save', function (next) {
  this.updated_at = Date.now();
  next();
});

const Bed = mongoose.model('Bed', bedSchema);

module.exports = Bed;