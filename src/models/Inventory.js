const mongoose = require('mongoose');

const inventorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
  },
  description: {
    type: String,
    required: false,
  },
  category: {
    type: String,
    required: true,
    enum: ['drug', 'medical_supply', 'equipment', 'other'],
  },
  unit: {
    type: String,
    required: true,
  },
  quantity: {
    type: Number,
    required: true,
    default: 0,
  },
  reorder_level: {
    type: Number,
    required: true,
    default: 10,
  },
  unit_price: {
    type: Number,
    required: true,
    default: 0,
  },
  supplier: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Supplier',
    required: false,
  },
  expiry_date: {
    type: Date,
    required: false,
  },
  batch_number: {
    type: String,
    required: false,
  },
  is_active: {
    type: Boolean,
    default: true,
  },
  created_at: {
    type: Date,
    default: Date.now,
  },
  updated_at: {
    type: Date,
    default: Date.now,
  },
});

inventorySchema.pre('save', function (next) {
  this.updated_at = Date.now();
  next();
});

const Inventory = mongoose.model('Inventory', inventorySchema);

module.exports = Inventory;