const mongoose = require('mongoose');

const patientSchema = new mongoose.Schema({
  first_name: {
    type: String,
    required: true,
    trim: true,
  },
  last_name: {
    type: String,
    required: true,
    trim: true,
  },
  dob: {
    type: Date,
    required: true,
  },
  gender: {
    type: String,
    enum: ['Male', 'Female', 'Other'],
    required: true,
  },
  phone: {
    type: String,
    required: true,
    unique: true,
  },
  email: {
    type: String,
    required: false,
    unique: false,
  },
  address: {
    type: String,
    required: false,
  },
  emergency_contact: {
    name: String,
    phone: String,
    relationship: String,
  },
  medical_history: [{
    condition: String,
    diagnosis_date: Date,
    treated_by: String,
    notes: String,
  }],
  insurance: {
    provider: String,
    policy_number: String,
  },
  created_at: {
    type: Date,
    default: Date.now,
  },
  updated_at: {
    type: Date,
    default: Date.now,
  },
});

patientSchema.pre('save', function (next) {
  this.updated_at = Date.now();
  next();
});

const Patient = mongoose.model('Patient', patientSchema);

module.exports = Patient;