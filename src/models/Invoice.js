const mongoose = require('mongoose');

const invoiceSchema = new mongoose.Schema({
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: true,
  },
  invoice_number: {
    type: String,
    required: true,
    unique: true,
  },
  invoice_date: {
    type: Date,
    required: true,
    default: Date.now,
  },
  due_date: {
    type: Date,
    required: false,
  },
  items: [{
    description: {
      type: String,
      required: true,
    },
    quantity: {
      type: Number,
      required: true,
      default: 1,
    },
    unit_price: {
      type: Number,
      required: true,
    },
    total: {
      type: Number,
      required: true,
    },
  }],
  subtotal: {
    type: Number,
    required: true,
  },
  tax: {
    type: Number,
    required: true,
    default: 0,
  },
  total_amount: {
    type: Number,
    required: true,
  },
  amount_paid: {
    type: Number,
    required: true,
    default: 0,
  },
  balance_due: {
    type: Number,
    required: true,
  },
  status: {
    type: String,
    enum: ['draft', 'sent', 'paid', 'overdue', 'cancelled'],
    default: 'draft',
  },
  notes: {
    type: String,
    required: false,
  },
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Staff',
    required: true,
  },
  created_at: {
    type: Date,
    default: Date.now,
  },
  updated_at: {
    type: Date,
    default: Date.now,
  },
});

invoiceSchema.pre('save', function (next) {
  this.updated_at = Date.now();
  next();
});

const Invoice = mongoose.model('Invoice', invoiceSchema);

module.exports = Invoice;