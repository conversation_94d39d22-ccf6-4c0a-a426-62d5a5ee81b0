const mongoose = require('mongoose');

const staffSchema = new mongoose.Schema({
  first_name: {
    type: String,
    required: true,
    trim: true,
  },
  last_name: {
    type: String,
    required: true,
    trim: true,
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
  },
  phone: {
    type: String,
    required: true,
  },
  role: {
    type: String,
    enum: ['admin', 'doctor', 'nurse', 'clerk'],
    required: true,
  },
  department: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Department',
    required: false,
  },
  specialization: {
    type: String,
    required: false,
  },
  license_number: {
    type: String,
    required: false,
  },
  hire_date: {
    type: Date,
    required: true,
    default: Date.now,
  },
  is_active: {
    type: Boolean,
    default: true,
  },
  created_at: {
    type: Date,
    default: Date.now,
  },
  updated_at: {
    type: Date,
    default: Date.now,
  },
});

staffSchema.pre('save', function (next) {
  this.updated_at = Date.now();
  next();
});

const Staff = mongoose.model('Staff', staffSchema);

module.exports = Staff;