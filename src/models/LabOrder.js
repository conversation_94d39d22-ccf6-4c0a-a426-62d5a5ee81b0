const mongoose = require('mongoose');

const labOrderSchema = new mongoose.Schema({
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: true,
  },
  doctor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Staff',
    required: true,
  },
  test_name: {
    type: String,
    required: true,
  },
  test_description: {
    type: String,
    required: false,
  },
  order_date: {
    type: Date,
    required: true,
    default: Date.now,
  },
  results: {
    type: String,
    required: false,
  },
  results_date: {
    type: Date,
    required: false,
  },
  status: {
    type: String,
    enum: ['ordered', 'in_progress', 'completed', 'cancelled'],
    default: 'ordered',
  },
  notes: {
    type: String,
    required: false,
  },
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Staff',
    required: true,
  },
  created_at: {
    type: Date,
    default: Date.now,
  },
  updated_at: {
    type: Date,
    default: Date.now,
  },
});

labOrderSchema.pre('save', function (next) {
  this.updated_at = Date.now();
  next();
});

const LabOrder = mongoose.model('LabOrder', labOrderSchema);

module.exports = LabOrder;