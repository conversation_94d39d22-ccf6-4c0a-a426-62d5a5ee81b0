const mongoose = require('mongoose');

const reportDailySchema = new mongoose.Schema({
  date: {
    type: Date,
    required: true,
    unique: true,
  },
  new_patients: {
    type: Number,
    required: true,
    default: 0,
  },
  appointments_total: {
    type: Number,
    required: true,
    default: 0,
  },
  appointments_completed: {
    type: Number,
    required: true,
    default: 0,
  },
  admissions_open: {
    type: Number,
    required: true,
    default: 0,
  },
  beds_available: {
    type: Number,
    required: true,
    default: 0,
  },
  revenue: {
    type: Number,
    required: true,
    default: 0,
  },
  accounts_receivable_balance: {
    type: Number,
    required: true,
    default: 0,
  },
  mpesa_total_confirmed: {
    type: Number,
    required: true,
    default: 0,
  },
  notes: {
    type: String,
    required: false,
  },
  created_at: {
    type: Date,
    default: Date.now,
  },
  updated_at: {
    type: Date,
    default: Date.now,
  },
});

reportDailySchema.pre('save', function (next) {
  this.updated_at = Date.now();
  next();
});

const ReportDaily = mongoose.model('ReportDaily', reportDailySchema);

module.exports = ReportDaily;