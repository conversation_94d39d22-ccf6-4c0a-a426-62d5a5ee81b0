const mongoose = require('mongoose');

const departmentSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true,
  },
  description: {
    type: String,
    required: false,
  },
  head: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Staff',
    required: false,
  },
  is_active: {
    type: Boolean,
    default: true,
  },
  created_at: {
    type: Date,
    default: Date.now,
  },
  updated_at: {
    type: Date,
    default: Date.now,
  },
});

departmentSchema.pre('save', function (next) {
  this.updated_at = Date.now();
  next();
});

const Department = mongoose.model('Department', departmentSchema);

module.exports = Department;