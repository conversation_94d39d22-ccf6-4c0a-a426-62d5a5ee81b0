# Hospital Management System (HMS) Production Deployment Guide

This guide provides detailed instructions for deploying the Hospital Management System (HMS) to production environments.

## Prerequisites

Before deploying, ensure you have:

1. A domain name (optional but recommended)
2. SSL certificate (required for production)
3. MongoDB database (MongoDB Atlas recommended)
4. Node.js 16+ installed on the server
5. Access to deployment platforms (Render, GitHub, etc.)

## Production Environment Setup

### 1. Environment Variables

Create a `.env.production` file with the following variables:

```env
# Server Configuration
PORT=5000
NODE_ENV=production

# Database Configuration (MongoDB Atlas recommended)
DB_URL=mongodb+srv://username:<EMAIL>/hms?retryWrites=true&w=majority

# JWT Configuration (use a strong, unique secret)
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d

# API Key for authentication (use a strong, unique key)
API_KEY=your-super-secret-api-key-change-in-production

# Email Configuration
EMAIL_API_KEY=your-email-provider-api-key
ADMIN_EMAIL=<EMAIL>

# Base URL (your domain)
BASE_URL=https://your-hospital-management-system.com

# Timezone
TIMEZONE=Africa/Nairobi

# M-Pesa Configuration (for production)
MPESA_CONSUMER_KEY=your-production-consumer-key
MPESA_CONSUMER_SECRET=your-production-consumer-secret
MPESA_PASSKEY=your-production-passkey
MPESA_SHORTCODE=your-production-shortcode
MPESA_ENV=production
MPESA_CALLBACK_URL=https://your-hospital-management-system.com/api/mpesa/callback
```

### 2. Security Considerations

1. **Change all default passwords and secrets**
2. **Use strong, unique secrets for JWT and API keys**
3. **Enable HTTPS (required for production)**
4. **Set up proper CORS configuration**
5. **Use helmet.js for additional security headers**
6. **Implement rate limiting (already included)**
7. **Regular security audits**

### 3. Performance Optimization

1. **Use a production-grade MongoDB instance**
2. **Implement caching for frequently accessed data**
3. **Use a CDN for static assets**
4. **Set up monitoring and logging**
5. **Optimize database indexes**
6. **Use compression middleware**

### 4. Monitoring and Logging

1. **Set up error tracking (e.g., Sentry)**
2. **Implement application performance monitoring**
3. **Set up health check endpoints (/health)**
4. **Configure log rotation**
5. **Set up alerts for critical errors**

## Deployment Platforms

### Render Deployment (Backend)

1. **Create a render.yaml file in the root directory:**
   ```yaml
   services:
     - type: web
       name: hms-backend
       env: node
       buildCommand: npm install
       startCommand: npm start
       envVars:
         - key: NODE_ENV
           value: production
         - key: DB_URL
           sync: false
         - key: JWT_SECRET
           sync: false
         - key: API_KEY
           sync: false
         - key: EMAIL_API_KEY
           sync: false
         - key: ADMIN_EMAIL
           sync: false
         - key: BASE_URL
           sync: false
         - key: MPESA_CONSUMER_KEY
           sync: false
         - key: MPESA_CONSUMER_SECRET
           sync: false
         - key: MPESA_PASSKEY
           sync: false
         - key: MPESA_SHORTCODE
           sync: false
         - key: MPESA_ENV
           sync: false
         - key: MPESA_CALLBACK_URL
           sync: false
   ```

2. **Deploy to Render:**
   - Go to https://render.com/
   - Connect your GitHub account
   - Create a new web service
   - Select your repository
   - Choose the branch to deploy
   - Set the root directory to the root of your project
   - Set environment variables in the Render dashboard
   - Click "Create Web Service"

3. **Configure environment variables in Render dashboard:**
   - DB_URL: Your MongoDB Atlas connection string
   - JWT_SECRET: Your JWT secret
   - API_KEY: Your API key
   - EMAIL_API_KEY: Your email provider API key
   - ADMIN_EMAIL: Admin email address
   - BASE_URL: Your backend URL (e.g., https://hms-backend.onrender.com)
   - MPESA_CONSUMER_KEY: Your M-Pesa consumer key
   - MPESA_CONSUMER_SECRET: Your M-Pesa consumer secret
   - MPESA_PASSKEY: Your M-Pesa passkey
   - MPESA_SHORTCODE: Your M-Pesa shortcode
   - MPESA_ENV: production
   - MPESA_CALLBACK_URL: Your callback URL (e.g., https://hms-backend.onrender.com/api/mpesa/callback)

### GitHub Pages Deployment (Frontend)

1. **Update the frontend to use the Render backend URL:**
   In your frontend code, update the API base URL to point to your Render backend:
   ```javascript
   // In a config file or as an environment variable
   const API_BASE_URL = 'https://your-render-backend-url.onrender.com';
   ```

2. **Install gh-pages package:**
   ```bash
   cd client
   npm install gh-pages --save-dev
   ```

3. **Update package.json in the client directory:**
   ```json
   {
     "name": "hms-frontend",
     "homepage": "https://your-github-username.github.io/your-repo-name",
     "scripts": {
       "start": "react-scripts start",
       "build": "react-scripts build",
       "test": "react-scripts test",
       "eject": "react-scripts eject",
       "predeploy": "npm run build",
       "deploy": "gh-pages -d build"
     }
   }
   ```

4. **Deploy to GitHub Pages:**
   ```bash
   cd client
   npm run deploy
   ```

5. **Configure GitHub Pages in your repository settings:**
   - Go to your repository on GitHub
   - Click on "Settings" tab
   - Scroll down to "Pages" section
   - Select "gh-pages" branch as the source
   - Click "Save"

### Heroku Deployment

1. **Prepare for Heroku deployment:**
   ```bash
   # Create a Procfile in the root directory (already created)
   echo "web: node server.js" > Procfile
   ```

2. **Deploy to Heroku:**
   ```bash
   # Install Heroku CLI if you haven't already
   # https://devcenter.heroku.com/articles/heroku-cli

   # Login to Heroku
   heroku login

   # Create a new Heroku app
   heroku create your-hms-app-name

   # Set environment variables
   heroku config:set NODE_ENV=production
   heroku config:set DB_URL=your-mongodb-atlas-uri
   heroku config:set JWT_SECRET=your-jwt-secret
   heroku config:set API_KEY=your-api-key
   heroku config:set EMAIL_API_KEY=your-email-api-key
   heroku config:set ADMIN_EMAIL=<EMAIL>
   heroku config:set BASE_URL=https://your-hms-app-name.herokuapp.com
   heroku config:set MPESA_CONSUMER_KEY=your-production-consumer-key
   heroku config:set MPESA_CONSUMER_SECRET=your-production-consumer-secret
   heroku config:set MPESA_PASSKEY=your-production-passkey
   heroku config:set MPESA_SHORTCODE=your-production-shortcode
   heroku config:set MPESA_ENV=production
   heroku config:set MPESA_CALLBACK_URL=https://your-hms-app-name.herokuapp.com/api/mpesa/callback

   # Enable automatic deployment from GitHub (optional)
   heroku git:remote -a your-hms-app-name

   # Deploy the app
   git add .
   git commit -m "Prepare for Heroku deployment"
   git push heroku main
   ```

### AWS Deployment

#### Using AWS Elastic Beanstalk

1. **Prepare your application:**
   - Create a zip file of your application
   - Exclude node_modules and other unnecessary files

2. **Deploy to Elastic Beanstalk:**
   - Go to the AWS Elastic Beanstalk console
   - Create a new application
   - Choose Node.js as the platform
   - Upload your zip file
   - Configure environment variables in the AWS console

#### Using EC2 Instance

1. **Launch an EC2 instance:**
   - Choose Ubuntu Server 20.04 LTS
   - Select an appropriate instance type (t3.micro or higher)
   - Configure security groups to allow HTTP (80) and HTTPS (443) traffic

2. **SSH into your EC2 instance:**
   ```bash
   ssh -i your-key.pem ubuntu@your-ec2-public-ip
   ```

3. **Install dependencies:**
   ```bash
   # Update package list
   sudo apt update

   # Install Node.js
   curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
   source ~/.bashrc
   nvm install node

   # Install MongoDB (optional, but recommended to use MongoDB Atlas)
   # Follow instructions at https://docs.mongodb.com/manual/tutorial/install-mongodb-on-ubuntu/

   # Install PM2 for process management
   npm install -g pm2
   ```

4. **Deploy the application:**
   ```bash
   # Clone your repository
   git clone your-repo-url
   cd hms-magic-app

   # Install dependencies
   npm install
   cd client && npm install && npm run build && cd ..

   # Set environment variables
   export DB_URL=your-mongodb-atlas-uri
   export JWT_SECRET=your-jwt-secret
   export API_KEY=your-api-key
   # ... (set all other environment variables)

   # Start the application with PM2
   pm2 start server.js --name hms-app

   # Save PM2 configuration
   pm2 save

   # Set PM2 to start on boot
   pm2 startup
   ```

5. **Set up Nginx as a reverse proxy (optional but recommended):**
   ```bash
   # Install Nginx
   sudo apt install nginx

   # Create Nginx configuration
   sudo nano /etc/nginx/sites-available/hms

   # Add the following configuration:
   server {
       listen 80;
       server_name your-domain.com;

       location / {
           proxy_pass http://localhost:5000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
       }
   }

   # Enable the site
   sudo ln -s /etc/nginx/sites-available/hms /etc/nginx/sites-enabled/

   # Test Nginx configuration
   sudo nginx -t

   # Restart Nginx
   sudo systemctl restart nginx
   ```

6. **Set up SSL with Let's Encrypt (recommended):**
   ```bash
   # Install Certbot
   sudo apt install certbot python3-certbot-nginx

   # Obtain SSL certificate
   sudo certbot --nginx -d your-domain.com

   # Test automatic renewal
   sudo certbot renew --dry-run
   ```

### Docker Deployment

1. **Build and run with docker-compose:**
   ```bash
   # Build and run with docker-compose
   docker-compose up -d

   # Check the logs
   docker-compose logs -f
   ```

2. **Deploy with Docker Swarm or Kubernetes (advanced):**
   - Create Docker Swarm cluster or Kubernetes cluster
   - Deploy the application using docker-compose.yml or Kubernetes manifests

## Database Setup

### MongoDB Atlas (Recommended for Production)

1. **Sign up for MongoDB Atlas:**
   - Go to https://www.mongodb.com/cloud/atlas
   - Create a free cluster
   - Add your IP address to the whitelist
   - Create a database user
   - Get your connection string

2. **Update your DB_URL environment variable:**
   ```
   DB_URL=mongodb+srv://username:<EMAIL>/hms?retryWrites=true&w=majority
   ```

3. **Set up database indexes:**
   ```bash
   # Connect to your MongoDB Atlas cluster
   # Create indexes for frequently queried fields
   db.patients.createIndex({ "phone": 1 }, { unique: true })
   db.staff.createIndex({ "email": 1 }, { unique: true })
   db.appointments.createIndex({ "appointment_date": 1, "doctor": 1 })
   # ... (create indexes for other collections as needed)
   ```

## Backup and Recovery

1. **Regular database backups:**
   - Use MongoDB Atlas backup feature
   - Or set up mongodump cron jobs

2. **Backup environment configurations:**
   - Store environment variables in a secure location
   - Use a secrets management system

3. **Test recovery procedures:**
   - Regularly test database restore procedures
   - Test application deployment from backups

## Maintenance

1. **Regular updates:**
   - Keep Node.js and dependencies up to date
   - Apply security patches promptly

2. **Monitor logs:**
   - Regularly check application logs for errors
   - Set up alerts for critical errors

3. **Performance monitoring:**
   - Monitor application performance
   - Optimize slow queries

4. **Security audits:**
   - Regularly audit security settings
   - Update secrets periodically

## Troubleshooting

1. **Application won't start:**
   - Check logs for error messages
   - Verify environment variables
   - Check database connectivity

2. **Database connection issues:**
   - Verify database URL
   - Check database user permissions
   - Verify IP whitelist settings

3. **Authentication issues:**
   - Verify JWT secret
   - Check API key
   - Verify user credentials

4. **M-Pesa integration issues:**
   - Verify M-Pesa credentials
   - Check callback URL accessibility
   - Check network connectivity to Safaricom servers

## Support

For support, please contact:
- Email: <EMAIL>
- Phone: +254 123 456 789

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.