version: '3.8'
services:
  app:
    build: .
    ports:
      - "5000:5000"
    environment:
      - DB_URL=mongodb://mongo:27017/hms
      - JWT_SECRET=your-jwt-secret
      - API_KEY=your-api-key
      - NODE_ENV=production
    depends_on:
      - mongo
    volumes:
      - ./logs:/usr/src/app/logs

  mongo:
    image: mongo
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db

volumes:
  mongo-data: