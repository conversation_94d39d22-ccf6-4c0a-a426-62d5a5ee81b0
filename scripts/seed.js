const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Import models
const Patient = require('../src/models/Patient');
const Staff = require('../src/models/Staff');
const Department = require('../src/models/Department');
const Bed = require('../src/models/Bed');
const Inventory = require('../src/models/Inventory');
const Supplier = require('../src/models/Supplier');

// Connect to database
mongoose.connect(process.env.DB_URL || 'mongodb://localhost:27017/hms', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => console.log('Connected to MongoDB'))
.catch((err) => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

// Sample data
const departments = [
  { name: 'Cardiology', description: 'Heart and cardiovascular system' },
  { name: 'Orthopedics', description: 'Bones and joints' },
  { name: 'Pediatrics', description: 'Children health' },
  { name: 'Emergency', description: 'Emergency care' },
  { name: 'Radiology', description: 'Medical imaging' },
];

const staff = [
  {
    first_name: 'Admin',
    last_name: 'User',
    email: '<EMAIL>',
    phone: '**********',
    role: 'admin',
    password: 'admin123',
  },
  {
    first_name: 'John',
    last_name: 'Smith',
    email: '<EMAIL>',
    phone: '**********',
    role: 'doctor',
    specialization: 'Cardiology',
    license_number: 'DOC001',
  },
  {
    first_name: 'Jane',
    last_name: 'Johnson',
    email: '<EMAIL>',
    phone: '**********',
    role: 'doctor',
    specialization: 'Orthopedics',
    license_number: 'DOC002',
  },
  {
    first_name: 'Emily',
    last_name: 'Williams',
    email: '<EMAIL>',
    phone: '**********',
    role: 'nurse',
  },
  {
    first_name: 'Michael',
    last_name: 'Brown',
    email: '<EMAIL>',
    phone: '**********',
    role: 'clerk',
  },
];

const patients = [
  {
    first_name: 'Alice',
    last_name: 'Cooper',
    dob: '1985-05-15',
    gender: 'Female',
    phone: '**********',
    email: '<EMAIL>',
    address: '123 Main St, Cityville',
  },
  {
    first_name: 'Bob',
    last_name: 'Builder',
    dob: '1990-08-22',
    gender: 'Male',
    phone: '**********',
    email: '<EMAIL>',
    address: '456 Oak Ave, Townsville',
  },
  {
    first_name: 'Carol',
    last_name: 'Danvers',
    dob: '1975-12-03',
    gender: 'Female',
    phone: '**********',
    email: '<EMAIL>',
    address: '789 Pine Rd, Villagetown',
  },
];

const beds = [
  { bed_number: '101', room_number: '101', ward: 'General', status: 'available' },
  { bed_number: '102', room_number: '101', ward: 'General', status: 'available' },
  { bed_number: '103', room_number: '102', ward: 'General', status: 'available' },
  { bed_number: '201', room_number: '201', ward: 'ICU', status: 'available' },
  { bed_number: '202', room_number: '201', ward: 'ICU', status: 'available' },
];

const suppliers = [
  { 
    name: 'MediSupply Ltd', 
    contact_person: 'David Wilson', 
    email: '<EMAIL>', 
    phone: '**********', 
    address: '1 Industrial Park, Supply City' 
  },
  { 
    name: 'PharmaCorp', 
    contact_person: 'Sarah Johnson', 
    email: '<EMAIL>', 
    phone: '**********', 
    address: '2 Medical Plaza, Drugville' 
  },
];

const inventory = [
  {
    name: 'Paracetamol',
    description: 'Pain reliever and fever reducer',
    category: 'drug',
    unit: 'tablet',
    quantity: 1000,
    reorder_level: 200,
    unit_price: 0.5,
    expiry_date: '2025-12-31',
    batch_number: 'PARA001',
  },
  {
    name: 'Bandages',
    description: 'Sterile gauze bandages',
    category: 'medical_supply',
    unit: 'pack',
    quantity: 200,
    reorder_level: 50,
    unit_price: 5.0,
    expiry_date: '2026-06-30',
    batch_number: 'BAND001',
  },
  {
    name: 'Sphygmomanometer',
    description: 'Blood pressure monitor',
    category: 'equipment',
    unit: 'unit',
    quantity: 10,
    reorder_level: 2,
    unit_price: 150.0,
  },
];

const seedDatabase = async () => {
  try {
    // Clear existing data
    await Patient.deleteMany({});
    await Staff.deleteMany({});
    await Department.deleteMany({});
    await Bed.deleteMany({});
    await Inventory.deleteMany({});
    await Supplier.deleteMany({});
    
    console.log('Existing data cleared');
    
    // Create departments
    const createdDepartments = await Department.insertMany(departments);
    console.log(`Created ${createdDepartments.length} departments`);
    
    // Create staff with hashed passwords
    const staffWithHashedPasswords = await Promise.all(staff.map(async (member) => {
      if (member.password) {
        member.password = await bcrypt.hash(member.password, 10);
      }
      return member;
    }));
    
    const createdStaff = await Staff.insertMany(staffWithHashedPasswords);
    console.log(`Created ${createdStaff.length} staff members`);
    
    // Assign departments to doctors
    const cardiologyDept = createdDepartments.find(d => d.name === 'Cardiology');
    const orthopedicsDept = createdDepartments.find(d => d.name === 'Orthopedics');
    
    const johnSmith = createdStaff.find(s => s.first_name === 'John' && s.last_name === 'Smith');
    const janeJohnson = createdStaff.find(s => s.first_name === 'Jane' && s.last_name === 'Jane');
    
    if (johnSmith && cardiologyDept) {
      johnSmith.department = cardiologyDept._id;
      await johnSmith.save();
    }
    
    if (janeJohnson && orthopedicsDept) {
      janeJohnson.department = orthopedicsDept._id;
      await janeJohnson.save();
    }
    
    // Create patients
    const createdPatients = await Patient.insertMany(patients);
    console.log(`Created ${createdPatients.length} patients`);
    
    // Create beds and assign departments
    const bedsWithDepartments = beds.map(bed => {
      if (bed.ward === 'ICU') {
        bed.department = cardiologyDept._id;
      } else {
        bed.department = orthopedicsDept._id;
      }
      return bed;
    });
    
    const createdBeds = await Bed.insertMany(bedsWithDepartments);
    console.log(`Created ${createdBeds.length} beds`);
    
    // Create suppliers
    const createdSuppliers = await Supplier.insertMany(suppliers);
    console.log(`Created ${createdSuppliers.length} suppliers`);
    
    // Create inventory and assign suppliers
    const inventoryWithSuppliers = inventory.map(item => {
      if (item.name === 'Paracetamol') {
        item.supplier = createdSuppliers[0]._id;
      } else if (item.name === 'Bandages') {
        item.supplier = createdSuppliers[1]._id;
      }
      return item;
    });
    
    const createdInventory = await Inventory.insertMany(inventoryWithSuppliers);
    console.log(`Created ${createdInventory.length} inventory items`);
    
    console.log('Database seeding completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
};

// Run the seed function
seedDatabase();