services:
  - type: web
    name: hms-backend
    env: node
    buildCommand: npm install
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: DB_URL
        sync: false
      - key: JWT_SECRET
        sync: false
      - key: API_KEY
        sync: false
      - key: EMAIL_API_KEY
        sync: false
      - key: ADMIN_EMAIL
        sync: false
      - key: BASE_URL
        sync: false
      - key: MPESA_CONSUMER_KEY
        sync: false
      - key: MPESA_CONSUMER_SECRET
        sync: false
      - key: MPESA_PASSKEY
        sync: false
      - key: MPESA_SHORTCODE
        sync: false
      - key: MPESA_ENV
        sync: false
      - key: MPESA_CALLBACK_URL
        sync: false