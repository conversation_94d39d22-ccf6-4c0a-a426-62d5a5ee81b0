# Hospital Management System (HMS) Magic App

A secure, responsive admin console and REST API for managing hospital operations, including patients, appointments, admissions, inventory, billing, lab tests, M-Pesa receipts, and daily reports.

## Features

- **Authentication & Authorization**: JWT or API Key with role claims (admin, doctor, nurse, clerk)
- **Protected Routes**: Via Authorization header (Bearer JWT or X-API-Key)
- **Rate Limiting**: 60 requests per minute per key
- **Responsive Design**: Mobile, tablet, and desktop support
- **Email Notifications**: On appointment creation/cancellation
- **M-Pesa Integration**: STK Push payments and callback handling
- **Scheduled Reports**: Daily KPI reports at 06:00

## API Endpoints

### Health Check

#### GET /health
Health check endpoint for monitoring

```bash
curl -X GET http://localhost:5000/health
```

### Authentication

#### POST /auth/login
Login to get JWT token

```bash
curl -X POST http://localhost:5000/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123"
  }'
```

#### GET /auth/verify
Verify JWT token

```bash
curl -X GET http://localhost:5000/auth/verify \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Patients

#### GET /api/patients
Get all patients with pagination and search

```bash
curl -X GET "http://localhost:5000/api/patients?page=1&limit=10&search=John" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### GET /api/patients/:id
Get patient by ID

```bash
curl -X GET http://localhost:5000/api/patients/PATIENT_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### POST /api/patients
Create new patient

```bash
curl -X POST http://localhost:5000/api/patients \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "first_name": "John",
    "last_name": "Doe",
    "dob": "1980-01-01",
    "gender": "Male",
    "phone": "**********",
    "email": "<EMAIL>"
  }'
```

#### PUT /api/patients/:id
Update patient

```bash
curl -X PUT http://localhost:5000/api/patients/PATIENT_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "first_name": "Jane",
    "last_name": "Doe"
  }'
```

#### DELETE /api/patients/:id
Delete patient

```bash
curl -X DELETE http://localhost:5000/api/patients/PATIENT_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Appointments

#### GET /api/appointments
Get all appointments

```bash
curl -X GET "http://localhost:5000/api/appointments?page=1&limit=10&status=scheduled" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### GET /api/appointments/calendar
Get appointments for calendar view

```bash
curl -X GET "http://localhost:5000/api/appointments/calendar?startDate=2023-01-01&endDate=2023-01-07" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### GET /api/appointments/:id
Get appointment by ID

```bash
curl -X GET http://localhost:5000/api/appointments/APPOINTMENT_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### POST /api/appointments
Create new appointment

```bash
curl -X POST http://localhost:5000/api/appointments \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "patient": "PATIENT_ID",
    "doctor": "DOCTOR_ID",
    "department": "DEPARTMENT_ID",
    "appointment_date": "2023-01-15",
    "appointment_time": "10:00",
    "reason": "Regular checkup"
  }'
```

#### PUT /api/appointments/:id
Update appointment

```bash
curl -X PUT http://localhost:5000/api/appointments/APPOINTMENT_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "completed",
    "notes": "Checkup completed successfully"
  }'
```

#### DELETE /api/appointments/:id
Delete appointment

```bash
curl -X DELETE http://localhost:5000/api/appointments/APPOINTMENT_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Admissions & Beds

#### GET /api/admissions
Get all admissions

```bash
curl -X GET "http://localhost:5000/api/admissions?page=1&limit=10&status=admitted" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### GET /api/admissions/:id
Get admission by ID

```bash
curl -X GET http://localhost:5000/api/admissions/ADMISSION_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### POST /api/admissions
Create new admission

```bash
curl -X POST http://localhost:5000/api/admissions \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "patient": "PATIENT_ID",
    "admission_date": "2023-01-15",
    "admitting_doctor": "DOCTOR_ID",
    "bed": "BED_ID",
    "department": "DEPARTMENT_ID",
    "reason": "Emergency admission"
  }'
```

#### PUT /api/admissions/:id/discharge
Discharge patient

```bash
curl -X PUT http://localhost:5000/api/admissions/ADMISSION_ID/discharge \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "discharge_date": "2023-01-20",
    "diagnosis": "Recovered from illness",
    "notes": "Patient discharged in good health"
  }'
```

#### PUT /api/admissions/:id
Update admission

```bash
curl -X PUT http://localhost:5000/api/admissions/ADMISSION_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "notes": "Patient condition improved"
  }'
```

#### DELETE /api/admissions/:id
Delete admission

```bash
curl -X DELETE http://localhost:5000/api/admissions/ADMISSION_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### GET /api/beds
Get all beds

```bash
curl -X GET "http://localhost:5000/api/beds?page=1&limit=10&status=available" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### GET /api/beds/available
Get available beds

```bash
curl -X GET http://localhost:5000/api/beds/available \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### GET /api/beds/:id
Get bed by ID

```bash
curl -X GET http://localhost:5000/api/beds/BED_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### POST /api/beds
Create new bed

```bash
curl -X POST http://localhost:5000/api/beds \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "bed_number": "301",
    "room_number": "301",
    "ward": "General",
    "department": "DEPARTMENT_ID"
  }'
```

#### PUT /api/beds/:id
Update bed

```bash
curl -X PUT http://localhost:5000/api/beds/BED_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "maintenance"
  }'
```

#### DELETE /api/beds/:id
Delete bed

```bash
curl -X DELETE http://localhost:5000/api/beds/BED_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Inventory

#### GET /api/inventory
Get all inventory items

```bash
curl -X GET "http://localhost:5000/api/inventory?page=1&limit=10&category=drug" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### GET /api/inventory/low-stock
Get low stock items

```bash
curl -X GET http://localhost:5000/api/inventory/low-stock \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### GET /api/inventory/:id
Get inventory item by ID

```bash
curl -X GET http://localhost:5000/api/inventory/INVENTORY_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### POST /api/inventory
Create new inventory item

```bash
curl -X POST http://localhost:5000/api/inventory \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Aspirin",
    "description": "Pain reliever",
    "category": "drug",
    "unit": "tablet",
    "quantity": 500,
    "reorder_level": 100,
    "unit_price": 0.75,
    "supplier": "SUPPLIER_ID"
  }'
```

#### PUT /api/inventory/:id
Update inventory item

```bash
curl -X PUT http://localhost:5000/api/inventory/INVENTORY_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "quantity": 400
  }'
```

#### PUT /api/inventory/:id/adjust
Adjust inventory quantity

```bash
curl -X PUT http://localhost:5000/api/inventory/INVENTORY_ID/adjust \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "quantity": 300,
    "reason": "Used in treatment"
  }'
```

#### DELETE /api/inventory/:id
Delete inventory item

```bash
curl -X DELETE http://localhost:5000/api/inventory/INVENTORY_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Billing & Payments

#### GET /api/invoices
Get all invoices

```bash
curl -X GET "http://localhost:5000/api/invoices?page=1&limit=10&status=paid" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### GET /api/invoices/:id
Get invoice by ID

```bash
curl -X GET http://localhost:5000/api/invoices/INVOICE_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### POST /api/invoices
Create new invoice

```bash
curl -X POST http://localhost:5000/api/invoices \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "patient": "PATIENT_ID",
    "invoice_date": "2023-01-15",
    "due_date": "2023-02-15",
    "items": [
      {
        "description": "Consultation fee",
        "quantity": 1,
        "unit_price": 1000
      }
    ],
    "tax": 100
  }'
```

#### PUT /api/invoices/:id
Update invoice

```bash
curl -X PUT http://localhost:5000/api/invoices/INVOICE_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "paid"
  }'
```

#### DELETE /api/invoices/:id
Delete invoice

```bash
curl -X DELETE http://localhost:5000/api/invoices/INVOICE_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### GET /api/payments
Get all payments

```bash
curl -X GET "http://localhost:5000/api/payments?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### GET /api/payments/:id
Get payment by ID

```bash
curl -X GET http://localhost:5000/api/payments/PAYMENT_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### POST /api/payments
Create new payment

```bash
curl -X POST http://localhost:5000/api/payments \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "invoice": "INVOICE_ID",
    "patient": "PATIENT_ID",
    "amount": 1100,
    "payment_method": "cash",
    "payment_date": "2023-01-15"
  }'
```

#### PUT /api/payments/:id
Update payment

```bash
curl -X PUT http://localhost:5000/api/payments/PAYMENT_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "notes": "Payment received in full"
  }'
```

#### DELETE /api/payments/:id
Delete payment

```bash
curl -X DELETE http://localhost:5000/api/payments/PAYMENT_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### M-Pesa Payments

#### POST /api/payments/mpesa/initiate
Initiate M-Pesa STK Push

```bash
curl -X POST http://localhost:5000/api/payments/mpesa/initiate \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "invoice_id": "INVOICE_ID",
    "phone_number": "254712345678",
    "amount": 500
  }'
```

#### POST /api/payments/mpesa/callback
M-Pesa callback endpoint (public)

```bash
# This endpoint is called by Safaricom when a payment is made
# It's not meant to be called manually
```

#### GET /api/mpesa/receipts
Get M-Pesa receipts

```bash
curl -X GET "http://localhost:5000/api/mpesa/receipts?page=1&limit=10&status=completed" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### GET /api/mpesa/receipts/:id
Get M-Pesa receipt by ID

```bash
curl -X GET http://localhost:5000/api/mpesa/receipts/RECEIPT_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Lab Tests

#### GET /api/lab-orders
Get all lab orders

```bash
curl -X GET "http://localhost:5000/api/lab-orders?page=1&limit=10&status=completed" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### GET /api/lab-orders/:id
Get lab order by ID

```bash
curl -X GET http://localhost:5000/api/lab-orders/LAB_ORDER_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### POST /api/lab-orders
Create new lab order

```bash
curl -X POST http://localhost:5000/api/lab-orders \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "patient": "PATIENT_ID",
    "doctor": "DOCTOR_ID",
    "test_name": "Complete Blood Count",
    "test_description": "Full blood panel test",
    "order_date": "2023-01-15"
  }'
```

#### PUT /api/lab-orders/:id
Update lab order

```bash
curl -X PUT http://localhost:5000/api/lab-orders/LAB_ORDER_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "results": "Normal blood count",
    "results_date": "2023-01-16",
    "status": "completed"
  }'
```

#### DELETE /api/lab-orders/:id
Delete lab order

```bash
curl -X DELETE http://localhost:5000/api/lab-orders/LAB_ORDER_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Reports

#### GET /api/reports/daily
Get daily KPI reports

```bash
curl -X GET "http://localhost:5000/api/reports/daily?dateFrom=2023-01-01&dateTo=2023-01-15" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### GET /api/reports/daily/:id
Get daily KPI report by ID

```bash
curl -X GET http://localhost:5000/api/reports/daily/REPORT_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
DB_URL=mongodb://localhost:27017/hms

# JWT Configuration
JWT_SECRET=hms_jwt_secret_key_change_in_production
JWT_EXPIRES_IN=7d

# API Key for authentication
API_KEY=hms_api_key_change_in_production

# Email Configuration
EMAIL_API_KEY=
ADMIN_EMAIL=<EMAIL>

# Base URL
BASE_URL=http://localhost:5000

# Timezone
TIMEZONE=Africa/Nairobi

# M-Pesa Configuration
MPESA_CONSUMER_KEY=your_mpESA_consumer_key
MPESA_CONSUMER_SECRET=your_mpESA_consumer_secret
MPESA_PASSKEY=your_mpESA_passkey
MPESA_SHORTCODE=your_mpESA_shortcode
MPESA_ENV=sandbox
MPESA_CALLBACK_URL=http://localhost:5000/api/mpesa/callback
```

## Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd hms-magic-app
   ```

2. Install dependencies:
   ```bash
   npm install
   cd client
   npm install
   cd ..
   ```

3. Set up MongoDB database

4. Create `.env` file with your configuration

5. Run database seeding script:
   ```bash
   node scripts/seed.js
   ```

6. Start the development server:
   ```bash
   npm run dev
   ```

7. Start the frontend development server:
   ```bash
   npm run client
   ```

## Deployment

### Render and GitHub Pages Deployment (Recommended)

You can deploy the backend to Render and the frontend to GitHub Pages for a cost-effective solution.

#### Backend Deployment (Render)

1. **Create a render.yaml file in the root directory (already created)**
2. **Deploy to Render:**
   - Go to https://render.com/
   - Connect your GitHub account
   - Create a new web service
   - Select your repository
   - Choose the branch to deploy
   - Set the root directory to the root of your project
   - Set environment variables in the Render dashboard
   - Click "Create Web Service"

3. **Configure environment variables in Render dashboard:**
   - DB_URL: Your MongoDB Atlas connection string
   - JWT_SECRET: Your JWT secret
   - API_KEY: Your API key
   - EMAIL_API_KEY: Your email provider API key
   - ADMIN_EMAIL: Admin email address
   - BASE_URL: Your backend URL (e.g., https://hms-backend.onrender.com)
   - MPESA_CONSUMER_KEY: Your M-Pesa consumer key
   - MPESA_CONSUMER_SECRET: Your M-Pesa consumer secret
   - MPESA_PASSKEY: Your M-Pesa passkey
   - MPESA_SHORTCODE: Your M-Pesa shortcode
   - MPESA_ENV: production
   - MPESA_CALLBACK_URL: Your callback URL (e.g., https://hms-backend.onrender.com/api/mpesa/callback)

#### Frontend Deployment (GitHub Pages)

1. **Update the frontend to use the Render backend URL:**
   In your frontend code, update the API base URL to point to your Render backend:
   ```javascript
   // In a config file or as an environment variable
   const API_BASE_URL = 'https://your-render-backend-url.onrender.com';
   ```

2. **Install gh-pages package:**
   ```bash
   cd client
   npm install gh-pages --save-dev
   ```

3. **Update package.json in the client directory (already done)**
4. **Deploy to GitHub Pages:**
   ```bash
   cd client
   npm run deploy
   ```

5. **Configure GitHub Pages in your repository settings:**
   - Go to your repository on GitHub
   - Click on "Settings" tab
   - Scroll down to "Pages" section
   - Select "gh-pages" branch as the source
   - Click "Save"

### Heroku Deployment

1. **Prepare for Heroku deployment:**
   ```bash
   # Create a Procfile in the root directory
   echo "web: node server.js" > Procfile
   ```

2. **Deploy to Heroku:**
   ```bash
   # Install Heroku CLI if you haven't already
   # https://devcenter.heroku.com/articles/heroku-cli

   # Login to Heroku
   heroku login

   # Create a new Heroku app
   heroku create your-hms-app-name

   # Set environment variables
   heroku config:set NODE_ENV=production
   heroku config:set DB_URL=your-mongodb-uri
   heroku config:set JWT_SECRET=your-jwt-secret
   heroku config:set API_KEY=your-api-key

   # Deploy the app
   git add .
   git commit -m "Prepare for Heroku deployment"
   git push heroku main
   ```

### AWS Deployment

1. **Using AWS Elastic Beanstalk:**
   - Create a zip file of your application
   - Upload to Elastic Beanstalk
   - Configure environment variables in the AWS console

2. **Using EC2 Instance:**
   ```bash
   # SSH into your EC2 instance
   ssh -i your-key.pem ec2-user@your-ec2-public-ip

   # Install Node.js and MongoDB
   curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
   source ~/.bashrc
   nvm install node
   sudo yum install mongodb-org

   # Clone your repository
   git clone your-repo-url
   cd hms-magic-app

   # Install dependencies
   npm install
   cd client && npm install && cd ..

   # Set environment variables
   export DB_URL=your-mongodb-uri
   export JWT_SECRET=your-jwt-secret
   export API_KEY=your-api-key

   # Start the application
   npm start
   ```

### Docker Deployment

1. **Create a Dockerfile in the root directory:**
   ```dockerfile
   FROM node:16

   # Create app directory
   WORKDIR /usr/src/app

   # Copy package files
   COPY package*.json ./

   # Install dependencies
   RUN npm install

   # Copy app source
   COPY . .

   # Build client
   RUN cd client && npm install && npm run build

   # Expose port
   EXPOSE 5000

   # Start the application
   CMD ["npm", "start"]
   ```

2. **Create a docker-compose.yml file:**
   ```yaml
   version: '3.8'
   services:
     app:
       build: .
       ports:
         - "5000:5000"
       environment:
         - DB_URL=mongodb://mongo:27017/hms
         - JWT_SECRET=your-jwt-secret
         - API_KEY=your-api-key
       depends_on:
         - mongo
       volumes:
         - ./logs:/usr/src/app/logs

     mongo:
       image: mongo
       ports:
         - "27017:27017"
       volumes:
         - mongo-data:/data/db

   volumes:
     mongo-data:
   ```

3. **Deploy with Docker:**
   ```bash
   # Build and run with docker-compose
   docker-compose up -d

   # Or build and run with Docker
   docker build -t hms-app .
   docker run -p 5000:5000 -e DB_URL=your-mongodb-uri hms-app
   ```

### MongoDB Atlas (For Database)

1. **Sign up for MongoDB Atlas:**
   - Go to https://www.mongodb.com/cloud/atlas
   - Create a free cluster
   - Add your IP address to the whitelist
   - Create a database user
   - Get your connection string

2. **Update your DB_URL environment variable:**
   ```
   DB_URL=mongodb+srv://username:<EMAIL>/hms?retryWrites=true&w=majority
   ```

### Environment Variables for Production

Make sure to set these environment variables in your deployment environment:

```env
# Server Configuration
PORT=5000
NODE_ENV=production

# Database Configuration (use MongoDB Atlas for production)
DB_URL=your-mongodb-atlas-uri

# JWT Configuration (change for production)
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# API Key for authentication (change for production)
API_KEY=your-super-secret-api-key

# Email Configuration
EMAIL_API_KEY=your-email-api-key
ADMIN_EMAIL=<EMAIL>

# Base URL
BASE_URL=https://your-deployed-app.com

# Timezone
TIMEZONE=Africa/Nairobi

# M-Pesa Configuration (for production)
MPESA_CONSUMER_KEY=your-production-consumer-key
MPESA_CONSUMER_SECRET=your-production-consumer-secret
MPESA_PASSKEY=your-production-passkey
MPESA_SHORTCODE=your-production-shortcode
MPESA_ENV=production
MPESA_CALLBACK_URL=https://your-deployed-app.com/api/mpesa/callback
```

### Production Considerations

1. **Security:**
   - Use strong, unique secrets for JWT and API keys
   - Enable HTTPS (most platforms provide this automatically)
   - Set up proper CORS configuration
   - Use helmet.js for additional security headers

2. **Performance:**
   - Use a production-grade MongoDB instance
   - Implement caching for frequently accessed data
   - Use a CDN for static assets
   - Set up monitoring and logging

3. **Monitoring:**
   - Set up error tracking (e.g., Sentry)
   - Implement application performance monitoring
   - Set up health check endpoints

4. **Backup:**
   - Regular database backups
   - Backup environment configurations

## Testing

Run tests with:
```bash
npm test
```

## License

MIT