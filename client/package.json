{"name": "client", "version": "0.1.0", "private": true, "homepage": "https://your-github-username.github.io/your-repo-name", "dependencies": {"react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.8.1", "react-scripts": "0.9.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test --env=jsdom", "eject": "react-scripts eject", "predeploy": "npm run build", "deploy": "gh-pages -d build"}, "devDependencies": {"gh-pages": "^4.0.0"}}