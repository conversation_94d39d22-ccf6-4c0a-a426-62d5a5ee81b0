import React, { useState, useEffect } from 'react';

const LabTests = () => {
  const [labOrders, setLabOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [search, setSearch] = useState('');
  const [filter, setFilter] = useState('all'); // 'all', 'ordered', 'in_progress', 'completed', 'cancelled'
  const [showModal, setShowModal] = useState(false);
  const [currentOrder, setCurrentOrder] = useState(null);

  // Fetch lab orders from API
  useEffect(() => {
    fetchLabOrders();
  }, [filter]);

  const fetchLabOrders = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      // Build query parameters
      const params = new URLSearchParams();
      if (filter !== 'all') params.append('status', filter);
      
      const response = await fetch(`/api/lab-orders?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setLabOrders(data.data);
      } else {
        setError(data.error || 'Failed to fetch lab orders');
      }
    } catch (err) {
      setError('An error occurred while fetching lab orders');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateOrder = () => {
    setCurrentOrder(null);
    setShowModal(true);
  };

  const handleEditOrder = (order) => {
    setCurrentOrder(order);
    setShowModal(true);
  };

  const handleDeleteOrder = async (id) => {
    if (window.confirm('Are you sure you want to delete this lab order?')) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`/api/lab-orders/${id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
        
        if (response.ok) {
          fetchLabOrders(); // Refresh the list
        } else {
          const data = await response.json();
          setError(data.error || 'Failed to delete lab order');
        }
      } catch (err) {
        setError('An error occurred while deleting lab order');
      }
    }
  };

  const LabOrderModal = () => {
    const [formData, setFormData] = useState({
      patient: '',
      doctor: '',
      test_name: '',
      test_description: '',
      order_date: '',
      results: '',
      results_date: '',
      status: 'ordered',
      notes: '',
    });

    useEffect(() => {
      if (currentOrder) {
        setFormData({
          patient: currentOrder.patient?._id || '',
          doctor: currentOrder.doctor?._id || '',
          test_name: currentOrder.test_name || '',
          test_description: currentOrder.test_description || '',
          order_date: currentOrder.order_date ? currentOrder.order_date.split('T')[0] : '',
          results: currentOrder.results || '',
          results_date: currentOrder.results_date ? currentOrder.results_date.split('T')[0] : '',
          status: currentOrder.status || 'ordered',
          notes: currentOrder.notes || '',
        });
      } else {
        setFormData({
          patient: '',
          doctor: '',
          test_name: '',
          test_description: '',
          order_date: '',
          results: '',
          results_date: '',
          status: 'ordered',
          notes: '',
        });
      }
    }, [currentOrder]);

    const handleChange = (e) => {
      setFormData({
        ...formData,
        [e.target.name]: e.target.value,
      });
    };

    const handleSubmit = async (e) => {
      e.preventDefault();
      
      try {
        const token = localStorage.getItem('token');
        const url = currentOrder ? `/api/lab-orders/${currentOrder._id}` : '/api/lab-orders';
        const method = currentOrder ? 'PUT' : 'POST';
        
        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify(formData),
        });
        
        const data = await response.json();
        
        if (response.ok) {
          fetchLabOrders(); // Refresh the list
          setShowModal(false);
        } else {
          setError(data.error || 'Failed to save lab order');
        }
      } catch (err) {
        setError('An error occurred while saving lab order');
      }
    };

    return (
      <div className="modal">
        <div className="modal-content">
          <span className="close" onClick={() => setShowModal(false)}>&times;</span>
          <h2>{currentOrder ? 'Edit Lab Order' : 'Create New Lab Order'}</h2>
          <form onSubmit={handleSubmit}>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="patient">Patient:</label>
                <select
                  id="patient"
                  name="patient"
                  value={formData.patient}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select Patient</option>
                  {/* In a real app, you would fetch patients from the API */}
                  <option value="patient1">John Doe</option>
                  <option value="patient2">Jane Smith</option>
                </select>
              </div>
              <div className="form-group">
                <label htmlFor="doctor">Doctor:</label>
                <select
                  id="doctor"
                  name="doctor"
                  value={formData.doctor}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select Doctor</option>
                  {/* In a real app, you would fetch doctors from the API */}
                  <option value="doctor1">Dr. Smith</option>
                  <option value="doctor2">Dr. Johnson</option>
                </select>
              </div>
            </div>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="test_name">Test Name:</label>
                <input
                  type="text"
                  id="test_name"
                  name="test_name"
                  value={formData.test_name}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="status">Status:</label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                >
                  <option value="ordered">Ordered</option>
                  <option value="in_progress">In Progress</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>
            </div>
            <div className="form-group">
              <label htmlFor="test_description">Test Description:</label>
              <textarea
                id="test_description"
                name="test_description"
                value={formData.test_description}
                onChange={handleChange}
              />
            </div>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="order_date">Order Date:</label>
                <input
                  type="date"
                  id="order_date"
                  name="order_date"
                  value={formData.order_date}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="results_date">Results Date:</label>
                <input
                  type="date"
                  id="results_date"
                  name="results_date"
                  value={formData.results_date}
                  onChange={handleChange}
                />
              </div>
            </div>
            <div className="form-group">
              <label htmlFor="results">Results:</label>
              <textarea
                id="results"
                name="results"
                value={formData.results}
                onChange={handleChange}
              />
            </div>
            <div className="form-group">
              <label htmlFor="notes">Notes:</label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleChange}
              />
            </div>
            <div className="form-actions">
              <button type="button" onClick={() => setShowModal(false)}>Cancel</button>
              <button type="submit">Save Lab Order</button>
            </div>
          </form>
        </div>
      </div>
    );
  };

  const filteredOrders = labOrders.filter(order =>
    order.test_name.toLowerCase().includes(search.toLowerCase()) ||
    order.patient?.first_name.toLowerCase().includes(search.toLowerCase()) ||
    order.patient?.last_name.toLowerCase().includes(search.toLowerCase()) ||
    order.doctor?.first_name.toLowerCase().includes(search.toLowerCase()) ||
    order.doctor?.last_name.toLowerCase().includes(search.toLowerCase()) ||
    order.results?.toLowerCase().includes(search.toLowerCase())
  );

  if (loading) return <div>Loading lab orders...</div>;
  if (error) return <div className="error-message">{error}</div>;

  return (
    <div className="lab-tests-container">
      <div className="lab-tests-header">
        <h2>Lab Tests</h2>
        <div className="lab-tests-actions">
          <div className="filters">
            <input
              type="text"
              placeholder="Search lab tests..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="search-input"
            />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Statuses</option>
              <option value="ordered">Ordered</option>
              <option value="in_progress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
          <button onClick={handleCreateOrder} className="btn-primary">
            Create Lab Order
          </button>
        </div>
      </div>
      
      <div className="lab-tests-list">
        <table>
          <thead>
            <tr>
              <th>Patient</th>
              <th>Doctor</th>
              <th>Test Name</th>
              <th>Order Date</th>
              <th>Status</th>
              <th>Results</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredOrders.map((order) => (
              <tr key={order._id}>
                <td>{order.patient?.first_name} {order.patient?.last_name}</td>
                <td>Dr. {order.doctor?.first_name} {order.doctor?.last_name}</td>
                <td>{order.test_name}</td>
                <td>{new Date(order.order_date).toLocaleDateString()}</td>
                <td>
                  <span className={`status ${order.status}`}>
                    {order.status}
                  </span>
                </td>
                <td>
                  {order.results ? (
                    <span className="results-summary">
                      {order.results.substring(0, 50)}...
                    </span>
                  ) : (
                    'No results yet'
                  )}
                </td>
                <td>
                  <button onClick={() => handleEditOrder(order)} className="btn-edit">
                    Edit
                  </button>
                  <button onClick={() => handleDeleteOrder(order._id)} className="btn-delete">
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {showModal && <LabOrderModal />}
    </div>
  );
};

export default LabTests;