import React, { useState, useEffect } from 'react';

const Admissions = () => {
  const [admissions, setAdmissions] = useState([]);
  const [beds, setBeds] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [search, setSearch] = useState('');
  const [filter, setFilter] = useState('all'); // 'all', 'admitted', 'discharged', 'transferred'
  const [showModal, setShowModal] = useState(false);
  const [showDischargeModal, setShowDischargeModal] = useState(false);
  const [currentAdmission, setCurrentAdmission] = useState(null);
  const [dischargeData, setDischargeData] = useState({ discharge_date: '', diagnosis: '', notes: '' });

  // Fetch admissions and beds from API
  useEffect(() => {
    fetchAdmissions();
    fetchBeds();
  }, [filter]);

  const fetchAdmissions = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      // Build query parameters
      const params = new URLSearchParams();
      if (filter !== 'all') params.append('status', filter);
      
      const response = await fetch(`/api/admissions?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setAdmissions(data.data);
      } else {
        setError(data.error || 'Failed to fetch admissions');
      }
    } catch (err) {
      setError('An error occurred while fetching admissions');
    } finally {
      setLoading(false);
    }
  };

  const fetchBeds = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/beds', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setBeds(data.data);
      } else {
        setError(data.error || 'Failed to fetch beds');
      }
    } catch (err) {
      setError('An error occurred while fetching beds');
    }
  };

  const handleCreateAdmission = () => {
    setCurrentAdmission(null);
    setShowModal(true);
  };

  const handleEditAdmission = (admission) => {
    setCurrentAdmission(admission);
    setShowModal(true);
  };

  const handleDeleteAdmission = async (id) => {
    if (window.confirm('Are you sure you want to delete this admission record?')) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`/api/admissions/${id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
        
        if (response.ok) {
          fetchAdmissions(); // Refresh the list
        } else {
          const data = await response.json();
          setError(data.error || 'Failed to delete admission');
        }
      } catch (err) {
        setError('An error occurred while deleting admission');
      }
    }
  };

  const handleDischargePatient = (admission) => {
    setCurrentAdmission(admission);
    setDischargeData({ 
      discharge_date: new Date().toISOString().split('T')[0],
      diagnosis: '',
      notes: '' 
    });
    setShowDischargeModal(true);
  };

  const submitDischarge = async (e) => {
    e.preventDefault();
    
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/admissions/${currentAdmission._id}/discharge`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(dischargeData),
      });
      
      const data = await response.json();
      
      if (response.ok) {
        fetchAdmissions(); // Refresh the list
        setShowDischargeModal(false);
      } else {
        setError(data.error || 'Failed to discharge patient');
      }
    } catch (err) {
      setError('An error occurred while discharging patient');
    }
  };

  const AdmissionModal = () => {
    const [formData, setFormData] = useState({
      patient: '',
      admission_date: '',
      discharge_date: '',
      admitting_doctor: '',
      bed: '',
      department: '',
      reason: '',
      diagnosis: '',
      status: 'admitted',
      notes: '',
    });

    useEffect(() => {
      if (currentAdmission) {
        setFormData({
          patient: currentAdmission.patient?._id || '',
          admission_date: currentAdmission.admission_date ? currentAdmission.admission_date.split('T')[0] : '',
          discharge_date: currentAdmission.discharge_date ? currentAdmission.discharge_date.split('T')[0] : '',
          admitting_doctor: currentAdmission.admitting_doctor?._id || '',
          bed: currentAdmission.bed?._id || '',
          department: currentAdmission.department?._id || '',
          reason: currentAdmission.reason || '',
          diagnosis: currentAdmission.diagnosis || '',
          status: currentAdmission.status || 'admitted',
          notes: currentAdmission.notes || '',
        });
      } else {
        setFormData({
          patient: '',
          admission_date: '',
          discharge_date: '',
          admitting_doctor: '',
          bed: '',
          department: '',
          reason: '',
          diagnosis: '',
          status: 'admitted',
          notes: '',
        });
      }
    }, [currentAdmission]);

    const handleChange = (e) => {
      setFormData({
        ...formData,
        [e.target.name]: e.target.value,
      });
    };

    const handleSubmit = async (e) => {
      e.preventDefault();
      
      try {
        const token = localStorage.getItem('token');
        const url = currentAdmission ? `/api/admissions/${currentAdmission._id}` : '/api/admissions';
        const method = currentAdmission ? 'PUT' : 'POST';
        
        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify(formData),
        });
        
        const data = await response.json();
        
        if (response.ok) {
          fetchAdmissions(); // Refresh the list
          setShowModal(false);
        } else {
          setError(data.error || 'Failed to save admission');
        }
      } catch (err) {
        setError('An error occurred while saving admission');
      }
    };

    // Get available beds
    const availableBeds = beds.filter(bed => bed.status === 'available');

    return (
      <div className="modal">
        <div className="modal-content">
          <span className="close" onClick={() => setShowModal(false)}>&times;</span>
          <h2>{currentAdmission ? 'Edit Admission' : 'Create New Admission'}</h2>
          <form onSubmit={handleSubmit}>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="patient">Patient:</label>
                <select
                  id="patient"
                  name="patient"
                  value={formData.patient}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select Patient</option>
                  {/* In a real app, you would fetch patients from the API */}
                  <option value="patient1">John Doe</option>
                  <option value="patient2">Jane Smith</option>
                </select>
              </div>
              <div className="form-group">
                <label htmlFor="admitting_doctor">Admitting Doctor:</label>
                <select
                  id="admitting_doctor"
                  name="admitting_doctor"
                  value={formData.admitting_doctor}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select Doctor</option>
                  {/* In a real app, you would fetch doctors from the API */}
                  <option value="doctor1">Dr. Smith</option>
                  <option value="doctor2">Dr. Johnson</option>
                </select>
              </div>
            </div>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="department">Department:</label>
                <select
                  id="department"
                  name="department"
                  value={formData.department}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select Department</option>
                  {/* In a real app, you would fetch departments from the API */}
                  <option value="dept1">Cardiology</option>
                  <option value="dept2">Orthopedics</option>
                </select>
              </div>
              <div className="form-group">
                <label htmlFor="bed">Bed:</label>
                <select
                  id="bed"
                  name="bed"
                  value={formData.bed}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select Bed</option>
                  {availableBeds.map(bed => (
                    <option key={bed._id} value={bed._id}>
                      {bed.bed_number} - {bed.ward}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="admission_date">Admission Date:</label>
                <input
                  type="date"
                  id="admission_date"
                  name="admission_date"
                  value={formData.admission_date}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="discharge_date">Discharge Date:</label>
                <input
                  type="date"
                  id="discharge_date"
                  name="discharge_date"
                  value={formData.discharge_date}
                  onChange={handleChange}
                />
              </div>
            </div>
            <div className="form-group">
              <label htmlFor="reason">Reason for Admission:</label>
              <input
                type="text"
                id="reason"
                name="reason"
                value={formData.reason}
                onChange={handleChange}
                required
              />
            </div>
            <div className="form-group">
              <label htmlFor="diagnosis">Diagnosis:</label>
              <input
                type="text"
                id="diagnosis"
                name="diagnosis"
                value={formData.diagnosis}
                onChange={handleChange}
              />
            </div>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="status">Status:</label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                >
                  <option value="admitted">Admitted</option>
                  <option value="discharged">Discharged</option>
                  <option value="transferred">Transferred</option>
                </select>
              </div>
            </div>
            <div className="form-group">
              <label htmlFor="notes">Notes:</label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleChange}
              />
            </div>
            <div className="form-actions">
              <button type="button" onClick={() => setShowModal(false)}>Cancel</button>
              <button type="submit">Save Admission</button>
            </div>
          </form>
        </div>
      </div>
    );
  };

  const DischargeModal = () => (
    <div className="modal">
      <div className="modal-content">
        <span className="close" onClick={() => setShowDischargeModal(false)}>&times;</span>
        <h2>Discharge Patient: {currentAdmission?.patient?.first_name} {currentAdmission?.patient?.last_name}</h2>
        <form onSubmit={submitDischarge}>
          <div className="form-group">
            <label htmlFor="discharge_date">Discharge Date:</label>
            <input
              type="date"
              id="discharge_date"
              value={dischargeData.discharge_date}
              onChange={(e) => setDischargeData({ ...dischargeData, discharge_date: e.target.value })}
              required
            />
          </div>
          <div className="form-group">
            <label htmlFor="diagnosis">Diagnosis:</label>
            <input
              type="text"
              id="diagnosis"
              value={dischargeData.diagnosis}
              onChange={(e) => setDischargeData({ ...dischargeData, diagnosis: e.target.value })}
              required
            />
          </div>
          <div className="form-group">
            <label htmlFor="notes">Notes:</label>
            <textarea
              id="notes"
              value={dischargeData.notes}
              onChange={(e) => setDischargeData({ ...dischargeData, notes: e.target.value })}
            />
          </div>
          <div className="form-actions">
            <button type="button" onClick={() => setShowDischargeModal(false)}>Cancel</button>
            <button type="submit">Discharge Patient</button>
          </div>
        </form>
      </div>
    </div>
  );

  const filteredAdmissions = admissions.filter(admission =>
    admission.patient?.first_name.toLowerCase().includes(search.toLowerCase()) ||
    admission.patient?.last_name.toLowerCase().includes(search.toLowerCase()) ||
    admission.reason.toLowerCase().includes(search.toLowerCase()) ||
    admission.diagnosis?.toLowerCase().includes(search.toLowerCase())
  );

  if (loading) return <div>Loading admissions...</div>;
  if (error) return <div className="error-message">{error}</div>;

  return (
    <div className="admissions-container">
      <div className="admissions-header">
        <h2>Admissions & Beds</h2>
        <div className="admissions-actions">
          <div className="filters">
            <input
              type="text"
              placeholder="Search admissions..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="search-input"
            />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Statuses</option>
              <option value="admitted">Admitted</option>
              <option value="discharged">Discharged</option>
              <option value="transferred">Transferred</option>
            </select>
          </div>
          <button onClick={handleCreateAdmission} className="btn-primary">
            Admit Patient
          </button>
        </div>
      </div>
      
      <div className="admissions-list">
        <table>
          <thead>
            <tr>
              <th>Patient</th>
              <th>Admission Date</th>
              <th>Discharge Date</th>
              <th>Doctor</th>
              <th>Bed</th>
              <th>Department</th>
              <th>Reason</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredAdmissions.map((admission) => (
              <tr key={admission._id}>
                <td>{admission.patient?.first_name} {admission.patient?.last_name}</td>
                <td>{new Date(admission.admission_date).toLocaleDateString()}</td>
                <td>
                  {admission.discharge_date 
                    ? new Date(admission.discharge_date).toLocaleDateString() 
                    : 'N/A'}
                </td>
                <td>Dr. {admission.admitting_doctor?.first_name} {admission.admitting_doctor?.last_name}</td>
                <td>{admission.bed?.bed_number} ({admission.bed?.ward})</td>
                <td>{admission.department?.name}</td>
                <td>{admission.reason}</td>
                <td>
                  <span className={`status ${admission.status}`}>
                    {admission.status}
                  </span>
                </td>
                <td>
                  {admission.status === 'admitted' && (
                    <button 
                      onClick={() => handleDischargePatient(admission)} 
                      className="btn-secondary"
                    >
                      Discharge
                    </button>
                  )}
                  <button onClick={() => handleEditAdmission(admission)} className="btn-edit">
                    Edit
                  </button>
                  <button onClick={() => handleDeleteAdmission(admission._id)} className="btn-delete">
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {showModal && <AdmissionModal />}
      {showDischargeModal && <DischargeModal />}
    </div>
  );
};

export default Admissions;