import React, { useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import { Link } from 'react-router-dom';

const Dashboard = () => {
  const { user } = useContext(AuthContext);

  // Define navigation items based on user role
  const getNavigationItems = () => {
    const items = [];

    if (user.role === 'admin') {
      items.push(
        { name: 'Dashboard', path: '/dashboard', icon: '📊' },
        { name: 'Patients', path: '/patients', icon: '👤' },
        { name: 'Appointments', path: '/appointments', icon: '📅' },
        { name: 'Admissions & Beds', path: '/admissions', icon: '🛏️' },
        { name: 'Inventory', path: '/inventory', icon: '📦' },
        { name: 'Billing & Payments', path: '/billing', icon: '💰' },
        { name: 'Lab Tests', path: '/lab-tests', icon: '🧪' },
        { name: 'M-Pesa Receipts', path: '/mpesa-receipts', icon: '📱' },
        { name: 'Reports', path: '/reports', icon: '📈' },
        { name: 'Staff Management', path: '/staff', icon: '👥' }
      );
    } else if (user.role === 'doctor') {
      items.push(
        { name: 'Dashboard', path: '/dashboard', icon: '📊' },
        { name: 'Patients', path: '/patients', icon: '👤' },
        { name: 'Appointments', path: '/appointments', icon: '📅' },
        { name: 'Admissions', path: '/admissions', icon: '🛏️' },
        { name: 'Lab Tests', path: '/lab-tests', icon: '🧪' },
        { name: 'Reports', path: '/reports', icon: '📈' }
      );
    } else if (user.role === 'nurse') {
      items.push(
        { name: 'Dashboard', path: '/dashboard', icon: '📊' },
        { name: 'Patients', path: '/patients', icon: '👤' },
        { name: 'Appointments', path: '/appointments', icon: '📅' },
        { name: 'Admissions', path: '/admissions', icon: '🛏️' },
        { name: 'Inventory', path: '/inventory', icon: '📦' },
        { name: 'Lab Tests', path: '/lab-tests', icon: '🧪' }
      );
    } else if (user.role === 'clerk') {
      items.push(
        { name: 'Dashboard', path: '/dashboard', icon: '📊' },
        { name: 'Patients', path: '/patients', icon: '👤' },
        { name: 'Appointments', path: '/appointments', icon: '📅' },
        { name: 'Inventory', path: '/inventory', icon: '📦' },
        { name: 'Billing & Payments', path: '/billing', icon: '💰' },
        { name: 'M-Pesa Receipts', path: '/mpesa-receipts', icon: '📱' }
      );
    }

    return items;
  };

  const navigationItems = getNavigationItems();

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h1>Hospital Management System</h1>
        <div className="user-info">
          <span>Welcome, {user.first_name} {user.last_name} ({user.role})</span>
        </div>
      </div>
      
      <div className="dashboard-content">
        <nav className="sidebar">
          <ul>
            {navigationItems.map((item) => (
              <li key={item.path}>
                <Link to={item.path}>
                  <span className="icon">{item.icon}</span>
                  <span className="nav-text">{item.name}</span>
                </Link>
              </li>
            ))}
          </ul>
        </nav>
        
        <main className="main-content">
          <div className="dashboard-widgets">
            <div className="widget">
              <h3>Quick Stats</h3>
              <div className="stats">
                <div className="stat-item">
                  <span className="stat-value">12</span>
                  <span className="stat-label">New Patients</span>
                </div>
                <div className="stat-item">
                  <span className="stat-value">5</span>
                  <span className="stat-label">Pending Appointments</span>
                </div>
                <div className="stat-item">
                  <span className="stat-value">3</span>
                  <span className="stat-label">Available Beds</span>
                </div>
                <div className="stat-item">
                  <span className="stat-value">8</span>
                  <span className="stat-label">Low Stock Items</span>
                </div>
              </div>
            </div>
            
            <div className="widget">
              <h3>Recent Activity</h3>
              <ul className="activity-list">
                <li>Patient John Doe registered 2 hours ago</li>
                <li>Appointment with Dr. Smith confirmed 3 hours ago</li>
                <li>Medication stock updated 5 hours ago</li>
                <li>Lab test results available for Patient Jane Smith</li>
              </ul>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Dashboard;