import React, { useState, useEffect } from 'react';

const Patients = () => {
  const [patients, setPatients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [search, setSearch] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [currentPatient, setCurrentPatient] = useState(null);

  // Fetch patients from API
  useEffect(() => {
    fetchPatients();
  }, []);

  const fetchPatients = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await fetch('/api/patients', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setPatients(data.data);
      } else {
        setError(data.error || 'Failed to fetch patients');
      }
    } catch (err) {
      setError('An error occurred while fetching patients');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    setSearch(e.target.value);
  };

  const filteredPatients = patients.filter(patient =>
    patient.first_name.toLowerCase().includes(search.toLowerCase()) ||
    patient.last_name.toLowerCase().includes(search.toLowerCase()) ||
    patient.phone.includes(search)
  );

  const handleCreatePatient = () => {
    setCurrentPatient(null);
    setShowModal(true);
  };

  const handleEditPatient = (patient) => {
    setCurrentPatient(patient);
    setShowModal(true);
  };

  const handleDeletePatient = async (id) => {
    if (window.confirm('Are you sure you want to delete this patient?')) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`/api/patients/${id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
        
        if (response.ok) {
          fetchPatients(); // Refresh the list
        } else {
          const data = await response.json();
          setError(data.error || 'Failed to delete patient');
        }
      } catch (err) {
        setError('An error occurred while deleting patient');
      }
    }
  };

  const PatientModal = () => {
    const [formData, setFormData] = useState({
      first_name: '',
      last_name: '',
      dob: '',
      gender: '',
      phone: '',
      email: '',
      address: '',
    });

    useEffect(() => {
      if (currentPatient) {
        setFormData({
          first_name: currentPatient.first_name || '',
          last_name: currentPatient.last_name || '',
          dob: currentPatient.dob ? currentPatient.dob.split('T')[0] : '',
          gender: currentPatient.gender || '',
          phone: currentPatient.phone || '',
          email: currentPatient.email || '',
          address: currentPatient.address || '',
        });
      } else {
        setFormData({
          first_name: '',
          last_name: '',
          dob: '',
          gender: '',
          phone: '',
          email: '',
          address: '',
        });
      }
    }, [currentPatient]);

    const handleChange = (e) => {
      setFormData({
        ...formData,
        [e.target.name]: e.target.value,
      });
    };

    const handleSubmit = async (e) => {
      e.preventDefault();
      
      try {
        const token = localStorage.getItem('token');
        const url = currentPatient ? `/api/patients/${currentPatient._id}` : '/api/patients';
        const method = currentPatient ? 'PUT' : 'POST';
        
        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify(formData),
        });
        
        const data = await response.json();
        
        if (response.ok) {
          fetchPatients(); // Refresh the list
          setShowModal(false);
        } else {
          setError(data.error || 'Failed to save patient');
        }
      } catch (err) {
        setError('An error occurred while saving patient');
      }
    };

    return (
      <div className="modal">
        <div className="modal-content">
          <span className="close" onClick={() => setShowModal(false)}>&times;</span>
          <h2>{currentPatient ? 'Edit Patient' : 'Create New Patient'}</h2>
          <form onSubmit={handleSubmit}>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="first_name">First Name:</label>
                <input
                  type="text"
                  id="first_name"
                  name="first_name"
                  value={formData.first_name}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="last_name">Last Name:</label>
                <input
                  type="text"
                  id="last_name"
                  name="last_name"
                  value={formData.last_name}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="dob">Date of Birth:</label>
                <input
                  type="date"
                  id="dob"
                  name="dob"
                  value={formData.dob}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="gender">Gender:</label>
                <select
                  id="gender"
                  name="gender"
                  value={formData.gender}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select Gender</option>
                  <option value="Male">Male</option>
                  <option value="Female">Female</option>
                  <option value="Other">Other</option>
                </select>
              </div>
            </div>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="phone">Phone:</label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="email">Email:</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                />
              </div>
            </div>
            <div className="form-group">
              <label htmlFor="address">Address:</label>
              <textarea
                id="address"
                name="address"
                value={formData.address}
                onChange={handleChange}
              />
            </div>
            <div className="form-actions">
              <button type="button" onClick={() => setShowModal(false)}>Cancel</button>
              <button type="submit">Save Patient</button>
            </div>
          </form>
        </div>
      </div>
    );
  };

  if (loading) return <div>Loading patients...</div>;
  if (error) return <div className="error-message">{error}</div>;

  return (
    <div className="patients-container">
      <div className="patients-header">
        <h2>Patients</h2>
        <div className="patients-actions">
          <input
            type="text"
            placeholder="Search patients..."
            value={search}
            onChange={handleSearch}
            className="search-input"
          />
          <button onClick={handleCreatePatient} className="btn-primary">
            Add New Patient
          </button>
        </div>
      </div>
      
      <div className="patients-list">
        <table>
          <thead>
            <tr>
              <th>Name</th>
              <th>Date of Birth</th>
              <th>Gender</th>
              <th>Phone</th>
              <th>Email</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredPatients.map((patient) => (
              <tr key={patient._id}>
                <td>{patient.first_name} {patient.last_name}</td>
                <td>{new Date(patient.dob).toLocaleDateString()}</td>
                <td>{patient.gender}</td>
                <td>{patient.phone}</td>
                <td>{patient.email}</td>
                <td>
                  <button onClick={() => handleEditPatient(patient)} className="btn-edit">
                    Edit
                  </button>
                  <button onClick={() => handleDeletePatient(patient._id)} className="btn-delete">
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {showModal && <PatientModal />}
    </div>
  );
};

export default Patients;