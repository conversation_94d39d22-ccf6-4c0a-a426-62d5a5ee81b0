import React, { useState } from 'react';

const MpesaPayment = ({ invoice, onClose, onPaymentSuccess }) => {
  const [paymentData, setPaymentData] = useState({
    invoice_id: invoice._id,
    phone_number: '',
    amount: invoice.balance_due,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  const handleChange = (e) => {
    setPaymentData({
      ...paymentData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess(false);

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/payments/mpesa/initiate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `<PERSON><PERSON> ${token}`,
        },
        body: JSON.stringify(paymentData),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess(true);
        // In a real app, you would handle the response and possibly redirect to a payment confirmation page
        // For now, we'll just show a success message
        setTimeout(() => {
          onPaymentSuccess(data.checkoutRequestID);
        }, 3000);
      } else {
        setError(data.error || 'Failed to initiate M-Pesa payment');
      }
    } catch (err) {
      setError('An error occurred while initiating M-Pesa payment');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="modal">
      <div className="modal-content">
        <span className="close" onClick={onClose}>&times;</span>
        <h2>Initiate M-Pesa Payment</h2>
        <p>Invoice: {invoice.invoice_number}</p>
        <p>Amount Due: KES {invoice.balance_due.toFixed(2)}</p>
        
        {success ? (
          <div className="success-message">
            <p>M-Pesa payment initiated successfully!</p>
            <p>Please check your phone for the payment prompt.</p>
            <p>You will be redirected shortly...</p>
          </div>
        ) : (
          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="phone_number">Phone Number:</label>
              <input
                type="tel"
                id="phone_number"
                name="phone_number"
                value={paymentData.phone_number}
                onChange={handleChange}
                placeholder="2547XXXXXXXX"
                required
              />
              <small>Enter phone number in international format (2547XXXXXXXX)</small>
            </div>
            <div className="form-group">
              <label htmlFor="amount">Amount:</label>
              <input
                type="number"
                id="amount"
                name="amount"
                value={paymentData.amount}
                onChange={handleChange}
                step="0.01"
                min="1"
                max={invoice.balance_due}
                required
              />
            </div>
            {error && <div className="error-message">{error}</div>}
            <div className="form-actions">
              <button type="button" onClick={onClose} disabled={loading}>
                Cancel
              </button>
              <button type="submit" disabled={loading}>
                {loading ? 'Processing...' : 'Initiate Payment'}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default MpesaPayment;