import React, { useState, useEffect } from 'react';

const Billing = () => {
  const [invoices, setInvoices] = useState([]);
  const [payments, setPayments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('invoices'); // 'invoices' or 'payments'
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [currentInvoice, setCurrentInvoice] = useState(null);
  const [currentPayment, setCurrentPayment] = useState(null);

  // Fetch invoices and payments from API
  useEffect(() => {
    fetchInvoices();
    fetchPayments();
  }, []);

  const fetchInvoices = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await fetch('/api/invoices', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setInvoices(data.data);
      } else {
        setError(data.error || 'Failed to fetch invoices');
      }
    } catch (err) {
      setError('An error occurred while fetching invoices');
    } finally {
      setLoading(false);
    }
  };

  const fetchPayments = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/payments', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setPayments(data.data);
      } else {
        setError(data.error || 'Failed to fetch payments');
      }
    } catch (err) {
      setError('An error occurred while fetching payments');
    }
  };

  const handleCreateInvoice = () => {
    setCurrentInvoice(null);
    setShowInvoiceModal(true);
  };

  const handleEditInvoice = (invoice) => {
    setCurrentInvoice(invoice);
    setShowInvoiceModal(true);
  };

  const handleDeleteInvoice = async (id) => {
    if (window.confirm('Are you sure you want to delete this invoice?')) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`/api/invoices/${id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
        
        if (response.ok) {
          fetchInvoices(); // Refresh the list
        } else {
          const data = await response.json();
          setError(data.error || 'Failed to delete invoice');
        }
      } catch (err) {
        setError('An error occurred while deleting invoice');
      }
    }
  };

  const handleCreatePayment = () => {
    setCurrentPayment(null);
    setShowPaymentModal(true);
  };

  const handleEditPayment = (payment) => {
    setCurrentPayment(payment);
    setShowPaymentModal(true);
  };

  const handleDeletePayment = async (id) => {
    if (window.confirm('Are you sure you want to delete this payment?')) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`/api/payments/${id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
        
        if (response.ok) {
          fetchPayments(); // Refresh the list
        } else {
          const data = await response.json();
          setError(data.error || 'Failed to delete payment');
        }
      } catch (err) {
        setError('An error occurred while deleting payment');
      }
    }
  };

  const InvoiceModal = () => {
    const [formData, setFormData] = useState({
      patient: '',
      invoice_date: '',
      due_date: '',
      items: [{ description: '', quantity: 1, unit_price: 0 }],
      tax: 0,
      notes: '',
      status: 'draft',
    });

    useEffect(() => {
      if (currentInvoice) {
        setFormData({
          patient: currentInvoice.patient?._id || '',
          invoice_date: currentInvoice.invoice_date ? currentInvoice.invoice_date.split('T')[0] : '',
          due_date: currentInvoice.due_date ? currentInvoice.due_date.split('T')[0] : '',
          items: currentInvoice.items || [{ description: '', quantity: 1, unit_price: 0 }],
          tax: currentInvoice.tax || 0,
          notes: currentInvoice.notes || '',
          status: currentInvoice.status || 'draft',
        });
      } else {
        setFormData({
          patient: '',
          invoice_date: '',
          due_date: '',
          items: [{ description: '', quantity: 1, unit_price: 0 }],
          tax: 0,
          notes: '',
          status: 'draft',
        });
      }
    }, [currentInvoice]);

    const handleChange = (e) => {
      setFormData({
        ...formData,
        [e.target.name]: e.target.value,
      });
    };

    const handleItemChange = (index, field, value) => {
      const newItems = [...formData.items];
      newItems[index][field] = value;
      setFormData({
        ...formData,
        items: newItems,
      });
    };

    const addItem = () => {
      setFormData({
        ...formData,
        items: [...formData.items, { description: '', quantity: 1, unit_price: 0 }],
      });
    };

    const removeItem = (index) => {
      const newItems = [...formData.items];
      newItems.splice(index, 1);
      setFormData({
        ...formData,
        items: newItems,
      });
    };

    const calculateSubtotal = () => {
      return formData.items.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0);
    };

    const calculateTotal = () => {
      return calculateSubtotal() + parseFloat(formData.tax || 0);
    };

    const handleSubmit = async (e) => {
      e.preventDefault();
      
      try {
        const token = localStorage.getItem('token');
        const url = currentInvoice ? `/api/invoices/${currentInvoice._id}` : '/api/invoices';
        const method = currentInvoice ? 'PUT' : 'POST';
        
        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify({
            ...formData,
            subtotal: calculateSubtotal(),
            total_amount: calculateTotal(),
            balance_due: calculateTotal() - (currentInvoice?.amount_paid || 0),
          }),
        });
        
        const data = await response.json();
        
        if (response.ok) {
          fetchInvoices(); // Refresh the list
          setShowInvoiceModal(false);
        } else {
          setError(data.error || 'Failed to save invoice');
        }
      } catch (err) {
        setError('An error occurred while saving invoice');
      }
    };

    return (
      <div className="modal">
        <div className="modal-content">
          <span className="close" onClick={() => setShowInvoiceModal(false)}>&times;</span>
          <h2>{currentInvoice ? 'Edit Invoice' : 'Create New Invoice'}</h2>
          <form onSubmit={handleSubmit}>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="patient">Patient:</label>
                <select
                  id="patient"
                  name="patient"
                  value={formData.patient}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select Patient</option>
                  {/* In a real app, you would fetch patients from the API */}
                  <option value="patient1">John Doe</option>
                  <option value="patient2">Jane Smith</option>
                </select>
              </div>
              <div className="form-group">
                <label htmlFor="status">Status:</label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                >
                  <option value="draft">Draft</option>
                  <option value="sent">Sent</option>
                  <option value="paid">Paid</option>
                  <option value="overdue">Overdue</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>
            </div>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="invoice_date">Invoice Date:</label>
                <input
                  type="date"
                  id="invoice_date"
                  name="invoice_date"
                  value={formData.invoice_date}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="due_date">Due Date:</label>
                <input
                  type="date"
                  id="due_date"
                  name="due_date"
                  value={formData.due_date}
                  onChange={handleChange}
                />
              </div>
            </div>
            
            <h3>Items</h3>
            {formData.items.map((item, index) => (
              <div key={index} className="item-row">
                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor={`item-description-${index}`}>Description:</label>
                    <input
                      type="text"
                      id={`item-description-${index}`}
                      value={item.description}
                      onChange={(e) => handleItemChange(index, 'description', e.target.value)}
                      required
                    />
                  </div>
                </div>
                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor={`item-quantity-${index}`}>Quantity:</label>
                    <input
                      type="number"
                      id={`item-quantity-${index}`}
                      value={item.quantity}
                      onChange={(e) => handleItemChange(index, 'quantity', parseInt(e.target.value))}
                      min="1"
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label htmlFor={`item-price-${index}`}>Unit Price:</label>
                    <input
                      type="number"
                      id={`item-price-${index}`}
                      value={item.unit_price}
                      onChange={(e) => handleItemChange(index, 'unit_price', parseFloat(e.target.value))}
                      step="0.01"
                      min="0"
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label>Total:</label>
                    <input
                      type="number"
                      value={(item.quantity * item.unit_price).toFixed(2)}
                      readOnly
                    />
                  </div>
                </div>
                {formData.items.length > 1 && (
                  <button type="button" onClick={() => removeItem(index)} className="btn-delete">
                    Remove
                  </button>
                )}
              </div>
            ))}
            <button type="button" onClick={addItem} className="btn-secondary">
              Add Item
            </button>
            
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="tax">Tax:</label>
                <input
                  type="number"
                  id="tax"
                  name="tax"
                  value={formData.tax}
                  onChange={handleChange}
                  step="0.01"
                  min="0"
                />
              </div>
            </div>
            
            <div className="form-row">
              <div className="form-group">
                <label>Subtotal:</label>
                <input
                  type="number"
                  value={calculateSubtotal().toFixed(2)}
                  readOnly
                />
              </div>
              <div className="form-group">
                <label>Total:</label>
                <input
                  type="number"
                  value={calculateTotal().toFixed(2)}
                  readOnly
                />
              </div>
            </div>
            
            <div className="form-group">
              <label htmlFor="notes">Notes:</label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleChange}
              />
            </div>
            
            <div className="form-actions">
              <button type="button" onClick={() => setShowInvoiceModal(false)}>Cancel</button>
              <button type="submit">Save Invoice</button>
            </div>
          </form>
        </div>
      </div>
    );
  };

  const PaymentModal = () => {
    const [formData, setFormData] = useState({
      invoice: '',
      patient: '',
      amount: '',
      payment_method: 'cash',
      payment_date: '',
      reference_number: '',
      notes: '',
    });

    useEffect(() => {
      if (currentPayment) {
        setFormData({
          invoice: currentPayment.invoice?._id || '',
          patient: currentPayment.patient?._id || '',
          amount: currentPayment.amount || '',
          payment_method: currentPayment.payment_method || 'cash',
          payment_date: currentPayment.payment_date ? currentPayment.payment_date.split('T')[0] : '',
          reference_number: currentPayment.reference_number || '',
          notes: currentPayment.notes || '',
        });
      } else {
        setFormData({
          invoice: '',
          patient: '',
          amount: '',
          payment_method: 'cash',
          payment_date: '',
          reference_number: '',
          notes: '',
        });
      }
    }, [currentPayment]);

    const handleChange = (e) => {
      setFormData({
        ...formData,
        [e.target.name]: e.target.value,
      });
    };

    const handleSubmit = async (e) => {
      e.preventDefault();
      
      try {
        const token = localStorage.getItem('token');
        const url = currentPayment ? `/api/payments/${currentPayment._id}` : '/api/payments';
        const method = currentPayment ? 'PUT' : 'POST';
        
        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify(formData),
        });
        
        const data = await response.json();
        
        if (response.ok) {
          fetchPayments(); // Refresh the list
          setShowPaymentModal(false);
        } else {
          setError(data.error || 'Failed to save payment');
        }
      } catch (err) {
        setError('An error occurred while saving payment');
      }
    };

    return (
      <div className="modal">
        <div className="modal-content">
          <span className="close" onClick={() => setShowPaymentModal(false)}>&times;</span>
          <h2>{currentPayment ? 'Edit Payment' : 'Record Payment'}</h2>
          <form onSubmit={handleSubmit}>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="invoice">Invoice:</label>
                <select
                  id="invoice"
                  name="invoice"
                  value={formData.invoice}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select Invoice</option>
                  {/* In a real app, you would fetch invoices from the API */}
                  <option value="invoice1">INV-20230101-1234</option>
                  <option value="invoice2">INV-20230102-5678</option>
                </select>
              </div>
              <div className="form-group">
                <label htmlFor="patient">Patient:</label>
                <select
                  id="patient"
                  name="patient"
                  value={formData.patient}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select Patient</option>
                  {/* In a real app, you would fetch patients from the API */}
                  <option value="patient1">John Doe</option>
                  <option value="patient2">Jane Smith</option>
                </select>
              </div>
            </div>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="amount">Amount:</label>
                <input
                  type="number"
                  id="amount"
                  name="amount"
                  value={formData.amount}
                  onChange={handleChange}
                  step="0.01"
                  min="0"
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="payment_method">Payment Method:</label>
                <select
                  id="payment_method"
                  name="payment_method"
                  value={formData.payment_method}
                  onChange={handleChange}
                >
                  <option value="cash">Cash</option>
                  <option value="mpesa">M-Pesa</option>
                  <option value="card">Card</option>
                  <option value="insurance">Insurance</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="payment_date">Payment Date:</label>
                <input
                  type="date"
                  id="payment_date"
                  name="payment_date"
                  value={formData.payment_date}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="reference_number">Reference Number:</label>
                <input
                  type="text"
                  id="reference_number"
                  name="reference_number"
                  value={formData.reference_number}
                  onChange={handleChange}
                />
              </div>
            </div>
            <div className="form-group">
              <label htmlFor="notes">Notes:</label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleChange}
              />
            </div>
            <div className="form-actions">
              <button type="button" onClick={() => setShowPaymentModal(false)}>Cancel</button>
              <button type="submit">Save Payment</button>
            </div>
          </form>
        </div>
      </div>
    );
  };

  const InvoicesList = () => (
    <div className="invoices-list">
      <table>
        <thead>
          <tr>
            <th>Invoice #</th>
            <th>Patient</th>
            <th>Date</th>
            <th>Due Date</th>
            <th>Total Amount</th>
            <th>Amount Paid</th>
            <th>Balance</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {invoices.map((invoice) => (
            <tr key={invoice._id}>
              <td>{invoice.invoice_number}</td>
              <td>{invoice.patient?.first_name} {invoice.patient?.last_name}</td>
              <td>{new Date(invoice.invoice_date).toLocaleDateString()}</td>
              <td>{invoice.due_date ? new Date(invoice.due_date).toLocaleDateString() : 'N/A'}</td>
              <td>${invoice.total_amount.toFixed(2)}</td>
              <td>${invoice.amount_paid.toFixed(2)}</td>
              <td>${invoice.balance_due.toFixed(2)}</td>
              <td>
                <span className={`status ${invoice.status}`}>
                  {invoice.status}
                </span>
              </td>
              <td>
                <button onClick={() => handleEditInvoice(invoice)} className="btn-edit">
                  Edit
                </button>
                <button onClick={() => handleDeleteInvoice(invoice._id)} className="btn-delete">
                  Delete
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  const PaymentsList = () => (
    <div className="payments-list">
      <table>
        <thead>
          <tr>
            <th>Invoice #</th>
            <th>Patient</th>
            <th>Amount</th>
            <th>Payment Method</th>
            <th>Date</th>
            <th>Reference #</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {payments.map((payment) => (
            <tr key={payment._id}>
              <td>{payment.invoice?.invoice_number}</td>
              <td>{payment.patient?.first_name} {payment.patient?.last_name}</td>
              <td>${payment.amount.toFixed(2)}</td>
              <td>{payment.payment_method}</td>
              <td>{new Date(payment.payment_date).toLocaleDateString()}</td>
              <td>{payment.reference_number || 'N/A'}</td>
              <td>
                <button onClick={() => handleEditPayment(payment)} className="btn-edit">
                  Edit
                </button>
                <button onClick={() => handleDeletePayment(payment._id)} className="btn-delete">
                  Delete
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  if (loading) return <div>Loading billing data...</div>;
  if (error) return <div className="error-message">{error}</div>;

  return (
    <div className="billing-container">
      <div className="billing-header">
        <h2>Billing & Payments</h2>
        <div className="billing-actions">
          <div className="tab-toggle">
            <button 
              className={activeTab === 'invoices' ? 'active' : ''} 
              onClick={() => setActiveTab('invoices')}
            >
              Invoices
            </button>
            <button 
              className={activeTab === 'payments' ? 'active' : ''} 
              onClick={() => setActiveTab('payments')}
            >
              Payments
            </button>
          </div>
          <div className="action-buttons">
            {activeTab === 'invoices' ? (
              <button onClick={handleCreateInvoice} className="btn-primary">
                Create Invoice
              </button>
            ) : (
              <button onClick={handleCreatePayment} className="btn-primary">
                Record Payment
              </button>
            )}
          </div>
        </div>
      </div>
      
      {activeTab === 'invoices' ? <InvoicesList /> : <PaymentsList />}
      
      {showInvoiceModal && <InvoiceModal />}
      {showPaymentModal && <PaymentModal />}
    </div>
  );
};

export default Billing;