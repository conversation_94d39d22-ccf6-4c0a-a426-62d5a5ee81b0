import React, { useState, useEffect } from 'react';

const MpesaReceipts = () => {
  const [receipts, setReceipts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [search, setSearch] = useState('');
  const [filter, setFilter] = useState('all'); // 'all', 'pending', 'completed', 'failed', 'reversed'
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');

  // Fetch M-Pesa receipts from API
  useEffect(() => {
    fetchReceipts();
  }, [filter, dateFrom, dateTo]);

  const fetchReceipts = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      // Build query parameters
      const params = new URLSearchParams();
      if (filter !== 'all') params.append('status', filter);
      if (dateFrom) params.append('dateFrom', dateFrom);
      if (dateTo) params.append('dateTo', dateTo);
      
      const response = await fetch(`/api/mpesa/receipts?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setReceipts(data.data);
      } else {
        setError(data.error || 'Failed to fetch M-Pesa receipts');
      }
    } catch (err) {
      setError('An error occurred while fetching M-Pesa receipts');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    setSearch(e.target.value);
  };

  const handleDateFilter = () => {
    fetchReceipts();
  };

  const clearDateFilter = () => {
    setDateFrom('');
    setDateTo('');
  };

  const filteredReceipts = receipts.filter(receipt =>
    receipt.phone_number.includes(search) ||
    receipt.mpesa_receipt_number?.includes(search) ||
    receipt.result_desc?.toLowerCase().includes(search.toLowerCase())
  );

  const ReceiptDetailsModal = ({ receipt, onClose }) => (
    <div className="modal">
      <div className="modal-content">
        <span className="close" onClick={onClose}>&times;</span>
        <h2>M-Pesa Receipt Details</h2>
        <div className="receipt-details">
          <div className="detail-row">
            <span className="label">Transaction ID:</span>
            <span className="value">{receipt.transaction_id}</span>
          </div>
          <div className="detail-row">
            <span className="label">M-Pesa Receipt Number:</span>
            <span className="value">{receipt.mpesa_receipt_number || 'N/A'}</span>
          </div>
          <div className="detail-row">
            <span className="label">Phone Number:</span>
            <span className="value">{receipt.phone_number}</span>
          </div>
          <div className="detail-row">
            <span className="label">Amount:</span>
            <span className="value">KES {receipt.amount.toFixed(2)}</span>
          </div>
          <div className="detail-row">
            <span className="label">Transaction Date:</span>
            <span className="value">{new Date(receipt.transaction_date).toLocaleString()}</span>
          </div>
          <div className="detail-row">
            <span className="label">Status:</span>
            <span className={`value status ${receipt.status}`}>{receipt.status}</span>
          </div>
          <div className="detail-row">
            <span className="label">Result Code:</span>
            <span className="value">{receipt.result_code || 'N/A'}</span>
          </div>
          <div className="detail-row">
            <span className="label">Result Description:</span>
            <span className="value">{receipt.result_desc || 'N/A'}</span>
          </div>
          <div className="detail-row">
            <span className="label">Patient:</span>
            <span className="value">
              {receipt.patient?.first_name} {receipt.patient?.last_name}
            </span>
          </div>
          <div className="detail-row">
            <span className="label">Invoice:</span>
            <span className="value">{receipt.invoice?.invoice_number || 'N/A'}</span>
          </div>
          <div className="detail-row">
            <span className="label">Checkout Request ID:</span>
            <span className="value">{receipt.checkout_request_id || 'N/A'}</span>
          </div>
          <div className="detail-row">
            <span className="label">Merchant Request ID:</span>
            <span className="value">{receipt.merchant_request_id || 'N/A'}</span>
          </div>
        </div>
      </div>
    </div>
  );

  const [selectedReceipt, setSelectedReceipt] = useState(null);

  if (loading) return <div>Loading M-Pesa receipts...</div>;
  if (error) return <div className="error-message">{error}</div>;

  return (
    <div className="mpesa-receipts-container">
      <div className="mpesa-receipts-header">
        <h2>M-Pesa Receipts</h2>
        <div className="mpesa-receipts-actions">
          <div className="filters">
            <input
              type="text"
              placeholder="Search receipts..."
              value={search}
              onChange={handleSearch}
              className="search-input"
            />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="completed">Completed</option>
              <option value="failed">Failed</option>
              <option value="reversed">Reversed</option>
            </select>
            <div className="date-filters">
              <label>
                From:
                <input
                  type="date"
                  value={dateFrom}
                  onChange={(e) => setDateFrom(e.target.value)}
                />
              </label>
              <label>
                To:
                <input
                  type="date"
                  value={dateTo}
                  onChange={(e) => setDateTo(e.target.value)}
                />
              </label>
              <button onClick={handleDateFilter} className="btn-secondary">
                Filter
              </button>
              <button onClick={clearDateFilter} className="btn-secondary">
                Clear
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <div className="mpesa-receipts-list">
        <table>
          <thead>
            <tr>
              <th>Receipt Number</th>
              <th>Phone Number</th>
              <th>Amount</th>
              <th>Date</th>
              <th>Status</th>
              <th>Result</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredReceipts.map((receipt) => (
              <tr key={receipt._id}>
                <td>{receipt.mpesa_receipt_number || 'N/A'}</td>
                <td>{receipt.phone_number}</td>
                <td>KES {receipt.amount.toFixed(2)}</td>
                <td>{new Date(receipt.transaction_date).toLocaleDateString()}</td>
                <td>
                  <span className={`status ${receipt.status}`}>
                    {receipt.status}
                  </span>
                </td>
                <td>{receipt.result_desc || 'N/A'}</td>
                <td>
                  <button 
                    onClick={() => setSelectedReceipt(receipt)} 
                    className="btn-view"
                  >
                    View Details
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {selectedReceipt && (
        <ReceiptDetailsModal 
          receipt={selectedReceipt} 
          onClose={() => setSelectedReceipt(null)} 
        />
      )}
    </div>
  );
};

export default MpesaReceipts;