import React, { useState, useEffect } from 'react';

const Staff = () => {
  const [staff, setStaff] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [search, setSearch] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [currentStaff, setCurrentStaff] = useState(null);

  // Fetch staff from API
  useEffect(() => {
    fetchStaff();
  }, []);

  const fetchStaff = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await fetch('/api/staff', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setStaff(data.data);
      } else {
        setError(data.error || 'Failed to fetch staff');
      }
    } catch (err) {
      setError('An error occurred while fetching staff');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateStaff = () => {
    setCurrentStaff(null);
    setShowModal(true);
  };

  const handleEditStaff = (staffMember) => {
    setCurrentStaff(staffMember);
    setShowModal(true);
  };

  const handleDeleteStaff = async (id) => {
    if (window.confirm('Are you sure you want to delete this staff member?')) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`/api/staff/${id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
        
        if (response.ok) {
          fetchStaff(); // Refresh the list
        } else {
          const data = await response.json();
          setError(data.error || 'Failed to delete staff member');
        }
      } catch (err) {
        setError('An error occurred while deleting staff member');
      }
    }
  };

  const StaffModal = () => {
    const [formData, setFormData] = useState({
      first_name: '',
      last_name: '',
      email: '',
      phone: '',
      role: 'doctor',
      department: '',
      specialization: '',
      license_number: '',
      hire_date: '',
      is_active: true,
      password: '',
    });

    useEffect(() => {
      if (currentStaff) {
        setFormData({
          first_name: currentStaff.first_name || '',
          last_name: currentStaff.last_name || '',
          email: currentStaff.email || '',
          phone: currentStaff.phone || '',
          role: currentStaff.role || 'doctor',
          department: currentStaff.department?._id || '',
          specialization: currentStaff.specialization || '',
          license_number: currentStaff.license_number || '',
          hire_date: currentStaff.hire_date ? currentStaff.hire_date.split('T')[0] : '',
          is_active: currentStaff.is_active !== undefined ? currentStaff.is_active : true,
          password: '',
        });
      } else {
        setFormData({
          first_name: '',
          last_name: '',
          email: '',
          phone: '',
          role: 'doctor',
          department: '',
          specialization: '',
          license_number: '',
          hire_date: '',
          is_active: true,
          password: '',
        });
      }
    }, [currentStaff]);

    const handleChange = (e) => {
      setFormData({
        ...formData,
        [e.target.name]: e.target.value,
      });
    };

    const handleSubmit = async (e) => {
      e.preventDefault();
      
      try {
        const token = localStorage.getItem('token');
        const url = currentStaff ? `/api/staff/${currentStaff._id}` : '/api/staff';
        const method = currentStaff ? 'PUT' : 'POST';
        
        // Remove password field if empty and editing
        const submitData = { ...formData };
        if (currentStaff && !submitData.password) {
          delete submitData.password;
        }
        
        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify(submitData),
        });
        
        const data = await response.json();
        
        if (response.ok) {
          fetchStaff(); // Refresh the list
          setShowModal(false);
        } else {
          setError(data.error || 'Failed to save staff member');
        }
      } catch (err) {
        setError('An error occurred while saving staff member');
      }
    };

    return (
      <div className="modal">
        <div className="modal-content">
          <span className="close" onClick={() => setShowModal(false)}>&times;</span>
          <h2>{currentStaff ? 'Edit Staff Member' : 'Create New Staff Member'}</h2>
          <form onSubmit={handleSubmit}>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="first_name">First Name:</label>
                <input
                  type="text"
                  id="first_name"
                  name="first_name"
                  value={formData.first_name}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="last_name">Last Name:</label>
                <input
                  type="text"
                  id="last_name"
                  name="last_name"
                  value={formData.last_name}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="email">Email:</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="phone">Phone:</label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="role">Role:</label>
                <select
                  id="role"
                  name="role"
                  value={formData.role}
                  onChange={handleChange}
                >
                  <option value="admin">Admin</option>
                  <option value="doctor">Doctor</option>
                  <option value="nurse">Nurse</option>
                  <option value="clerk">Clerk</option>
                </select>
              </div>
              <div className="form-group">
                <label htmlFor="department">Department:</label>
                <select
                  id="department"
                  name="department"
                  value={formData.department}
                  onChange={handleChange}
                >
                  <option value="">Select Department</option>
                  {/* In a real app, you would fetch departments from the API */}
                  <option value="dept1">Cardiology</option>
                  <option value="dept2">Orthopedics</option>
                </select>
              </div>
            </div>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="specialization">Specialization:</label>
                <input
                  type="text"
                  id="specialization"
                  name="specialization"
                  value={formData.specialization}
                  onChange={handleChange}
                />
              </div>
              <div className="form-group">
                <label htmlFor="license_number">License Number:</label>
                <input
                  type="text"
                  id="license_number"
                  name="license_number"
                  value={formData.license_number}
                  onChange={handleChange}
                />
              </div>
            </div>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="hire_date">Hire Date:</label>
                <input
                  type="date"
                  id="hire_date"
                  name="hire_date"
                  value={formData.hire_date}
                  onChange={handleChange}
                />
              </div>
              <div className="form-group">
                <label htmlFor="is_active">Active:</label>
                <input
                  type="checkbox"
                  id="is_active"
                  name="is_active"
                  checked={formData.is_active}
                  onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                />
              </div>
            </div>
            <div className="form-group">
              <label htmlFor="password">
                {currentStaff ? 'New Password (leave blank to keep current)' : 'Password'}:
              </label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                {...(currentStaff ? {} : { required: true })}
              />
            </div>
            <div className="form-actions">
              <button type="button" onClick={() => setShowModal(false)}>Cancel</button>
              <button type="submit">Save Staff Member</button>
            </div>
          </form>
        </div>
      </div>
    );
  };

  const filteredStaff = staff.filter(member =>
    member.first_name.toLowerCase().includes(search.toLowerCase()) ||
    member.last_name.toLowerCase().includes(search.toLowerCase()) ||
    member.email.toLowerCase().includes(search.toLowerCase()) ||
    member.role.toLowerCase().includes(search.toLowerCase())
  );

  if (loading) return <div>Loading staff...</div>;
  if (error) return <div className="error-message">{error}</div>;

  return (
    <div className="staff-container">
      <div className="staff-header">
        <h2>Staff Management</h2>
        <div className="staff-actions">
          <input
            type="text"
            placeholder="Search staff..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="search-input"
          />
          <button onClick={handleCreateStaff} className="btn-primary">
            Add New Staff
          </button>
        </div>
      </div>
      
      <div className="staff-list">
        <table>
          <thead>
            <tr>
              <th>Name</th>
              <th>Email</th>
              <th>Phone</th>
              <th>Role</th>
              <th>Department</th>
              <th>Specialization</th>
              <th>Hire Date</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredStaff.map((member) => (
              <tr key={member._id}>
                <td>{member.first_name} {member.last_name}</td>
                <td>{member.email}</td>
                <td>{member.phone}</td>
                <td>{member.role}</td>
                <td>{member.department?.name || 'N/A'}</td>
                <td>{member.specialization || 'N/A'}</td>
                <td>{new Date(member.hire_date).toLocaleDateString()}</td>
                <td>
                  {member.is_active ? (
                    <span className="status active">Active</span>
                  ) : (
                    <span className="status inactive">Inactive</span>
                  )}
                </td>
                <td>
                  <button onClick={() => handleEditStaff(member)} className="btn-edit">
                    Edit
                  </button>
                  <button onClick={() => handleDeleteStaff(member._id)} className="btn-delete">
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {showModal && <StaffModal />}
    </div>
  );
};

export default Staff;