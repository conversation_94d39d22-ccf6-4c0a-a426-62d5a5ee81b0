import React, { useState, useEffect } from 'react';
import { Bar, Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend
);

const Reports = () => {
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  const [chartData, setChartData] = useState(null);

  // Fetch reports from API
  useEffect(() => {
    fetchReports();
  }, [dateFrom, dateTo]);

  const fetchReports = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      // Build query parameters
      const params = new URLSearchParams();
      if (dateFrom) params.append('dateFrom', dateFrom);
      if (dateTo) params.append('dateTo', dateTo);
      
      const response = await fetch(`/api/reports/daily?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setReports(data.data);
        prepareChartData(data.data);
      } else {
        setError(data.error || 'Failed to fetch reports');
      }
    } catch (err) {
      setError('An error occurred while fetching reports');
    } finally {
      setLoading(false);
    }
  };

  const prepareChartData = (reportsData) => {
    if (!reportsData || reportsData.length === 0) return;

    // Sort by date
    const sortedReports = [...reportsData].sort((a, b) => 
      new Date(a.date) - new Date(b.date)
    );

    const labels = sortedReports.map(report => 
      new Date(report.date).toLocaleDateString()
    );
    
    const newPatientsData = sortedReports.map(report => report.new_patients);
    const appointmentsData = sortedReports.map(report => report.appointments_total);
    const revenueData = sortedReports.map(report => report.revenue);
    const mpesaData = sortedReports.map(report => report.mpesa_total_confirmed);

    setChartData({
      labels,
      datasets: [
        {
          label: 'New Patients',
          data: newPatientsData,
          backgroundColor: 'rgba(54, 162, 235, 0.6)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1,
        },
        {
          label: 'Appointments',
          data: appointmentsData,
          backgroundColor: 'rgba(255, 99, 132, 0.6)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1,
        },
        {
          label: 'Revenue (KES)',
          data: revenueData,
          backgroundColor: 'rgba(75, 192, 192, 0.6)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1,
        },
        {
          label: 'M-Pesa Payments',
          data: mpesaData,
          backgroundColor: 'rgba(153, 102, 255, 0.6)',
          borderColor: 'rgba(153, 102, 255, 1)',
          borderWidth: 1,
        },
      ],
    });
  };

  const handleDateFilter = () => {
    fetchReports();
  };

  const clearDateFilter = () => {
    setDateFrom('');
    setDateTo('');
  };

  const exportToCSV = () => {
    // Create CSV content
    let csvContent = "Date,New Patients,Total Appointments,Completed Appointments,Open Admissions,Available Beds,Revenue (KES),AR Balance (KES),M-Pesa Total\n";
    
    reports.forEach(report => {
      csvContent += `${report.date},${report.new_patients},${report.appointments_total},${report.appointments_completed},${report.admissions_open},${report.beds_available},${report.revenue},${report.accounts_receivable_balance},${report.mpesa_total_confirmed}\n`;
    });
    
    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'daily_kpi_report.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Daily KPIs',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  if (loading) return <div>Loading reports...</div>;
  if (error) return <div className="error-message">{error}</div>;

  return (
    <div className="reports-container">
      <div className="reports-header">
        <h2>Daily KPI Reports</h2>
        <div className="reports-actions">
          <div className="date-filters">
            <label>
              From:
              <input
                type="date"
                value={dateFrom}
                onChange={(e) => setDateFrom(e.target.value)}
              />
            </label>
            <label>
              To:
              <input
                type="date"
                value={dateTo}
                onChange={(e) => setDateTo(e.target.value)}
              />
            </label>
            <button onClick={handleDateFilter} className="btn-secondary">
              Filter
            </button>
            <button onClick={clearDateFilter} className="btn-secondary">
              Clear
            </button>
            <button onClick={exportToCSV} className="btn-primary">
              Export to CSV
            </button>
          </div>
        </div>
      </div>
      
      {chartData && (
        <div className="charts-container">
          <div className="chart-wrapper">
            <Bar data={chartData} options={chartOptions} />
          </div>
          <div className="chart-wrapper">
            <Line data={chartData} options={chartOptions} />
          </div>
        </div>
      )}
      
      <div className="reports-list">
        <table>
          <thead>
            <tr>
              <th>Date</th>
              <th>New Patients</th>
              <th>Total Appointments</th>
              <th>Completed Appointments</th>
              <th>Open Admissions</th>
              <th>Available Beds</th>
              <th>Revenue (KES)</th>
              <th>AR Balance (KES)</th>
              <th>M-Pesa Total</th>
            </tr>
          </thead>
          <tbody>
            {reports.map((report) => (
              <tr key={report._id}>
                <td>{new Date(report.date).toLocaleDateString()}</td>
                <td>{report.new_patients}</td>
                <td>{report.appointments_total}</td>
                <td>{report.appointments_completed}</td>
                <td>{report.admissions_open}</td>
                <td>{report.beds_available}</td>
                <td>{report.revenue.toFixed(2)}</td>
                <td>{report.accounts_receivable_balance.toFixed(2)}</td>
                <td>{report.mpesa_total_confirmed.toFixed(2)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Reports;