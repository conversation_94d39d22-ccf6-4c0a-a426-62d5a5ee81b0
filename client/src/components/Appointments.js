import React, { useState, useEffect } from 'react';

const Appointments = () => {
  const [appointments, setAppointments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [view, setView] = useState('list'); // 'list' or 'calendar'
  const [showModal, setShowModal] = useState(false);
  const [currentAppointment, setCurrentAppointment] = useState(null);

  // Fetch appointments from API
  useEffect(() => {
    fetchAppointments();
  }, []);

  const fetchAppointments = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await fetch('/api/appointments', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setAppointments(data.data);
      } else {
        setError(data.error || 'Failed to fetch appointments');
      }
    } catch (err) {
      setError('An error occurred while fetching appointments');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAppointment = () => {
    setCurrentAppointment(null);
    setShowModal(true);
  };

  const handleEditAppointment = (appointment) => {
    setCurrentAppointment(appointment);
    setShowModal(true);
  };

  const handleDeleteAppointment = async (id) => {
    if (window.confirm('Are you sure you want to delete this appointment?')) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`/api/appointments/${id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
        
        if (response.ok) {
          fetchAppointments(); // Refresh the list
        } else {
          const data = await response.json();
          setError(data.error || 'Failed to delete appointment');
        }
      } catch (err) {
        setError('An error occurred while deleting appointment');
      }
    }
  };

  const AppointmentModal = () => {
    const [formData, setFormData] = useState({
      patient: '',
      doctor: '',
      department: '',
      appointment_date: '',
      appointment_time: '',
      duration: 30,
      reason: '',
      notes: '',
      status: 'scheduled',
    });

    useEffect(() => {
      if (currentAppointment) {
        setFormData({
          patient: currentAppointment.patient?._id || '',
          doctor: currentAppointment.doctor?._id || '',
          department: currentAppointment.department?._id || '',
          appointment_date: currentAppointment.appointment_date ? currentAppointment.appointment_date.split('T')[0] : '',
          appointment_time: currentAppointment.appointment_time || '',
          duration: currentAppointment.duration || 30,
          reason: currentAppointment.reason || '',
          notes: currentAppointment.notes || '',
          status: currentAppointment.status || 'scheduled',
        });
      } else {
        setFormData({
          patient: '',
          doctor: '',
          department: '',
          appointment_date: '',
          appointment_time: '',
          duration: 30,
          reason: '',
          notes: '',
          status: 'scheduled',
        });
      }
    }, [currentAppointment]);

    const handleChange = (e) => {
      setFormData({
        ...formData,
        [e.target.name]: e.target.value,
      });
    };

    const handleSubmit = async (e) => {
      e.preventDefault();
      
      try {
        const token = localStorage.getItem('token');
        const url = currentAppointment ? `/api/appointments/${currentAppointment._id}` : '/api/appointments';
        const method = currentAppointment ? 'PUT' : 'POST';
        
        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify(formData),
        });
        
        const data = await response.json();
        
        if (response.ok) {
          fetchAppointments(); // Refresh the list
          setShowModal(false);
        } else {
          setError(data.error || 'Failed to save appointment');
        }
      } catch (err) {
        setError('An error occurred while saving appointment');
      }
    };

    return (
      <div className="modal">
        <div className="modal-content">
          <span className="close" onClick={() => setShowModal(false)}>&times;</span>
          <h2>{currentAppointment ? 'Edit Appointment' : 'Create New Appointment'}</h2>
          <form onSubmit={handleSubmit}>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="patient">Patient:</label>
                <select
                  id="patient"
                  name="patient"
                  value={formData.patient}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select Patient</option>
                  {/* In a real app, you would fetch patients from the API */}
                  <option value="patient1">John Doe</option>
                  <option value="patient2">Jane Smith</option>
                </select>
              </div>
              <div className="form-group">
                <label htmlFor="doctor">Doctor:</label>
                <select
                  id="doctor"
                  name="doctor"
                  value={formData.doctor}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select Doctor</option>
                  {/* In a real app, you would fetch doctors from the API */}
                  <option value="doctor1">Dr. Smith</option>
                  <option value="doctor2">Dr. Johnson</option>
                </select>
              </div>
            </div>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="department">Department:</label>
                <select
                  id="department"
                  name="department"
                  value={formData.department}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select Department</option>
                  {/* In a real app, you would fetch departments from the API */}
                  <option value="dept1">Cardiology</option>
                  <option value="dept2">Orthopedics</option>
                </select>
              </div>
              <div className="form-group">
                <label htmlFor="status">Status:</label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                >
                  <option value="scheduled">Scheduled</option>
                  <option value="confirmed">Confirmed</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                  <option value="no-show">No Show</option>
                </select>
              </div>
            </div>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="appointment_date">Date:</label>
                <input
                  type="date"
                  id="appointment_date"
                  name="appointment_date"
                  value={formData.appointment_date}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="appointment_time">Time:</label>
                <input
                  type="time"
                  id="appointment_time"
                  name="appointment_time"
                  value={formData.appointment_time}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="duration">Duration (minutes):</label>
                <input
                  type="number"
                  id="duration"
                  name="duration"
                  value={formData.duration}
                  onChange={handleChange}
                  min="15"
                  max="120"
                />
              </div>
            </div>
            <div className="form-group">
              <label htmlFor="reason">Reason:</label>
              <input
                type="text"
                id="reason"
                name="reason"
                value={formData.reason}
                onChange={handleChange}
                required
              />
            </div>
            <div className="form-group">
              <label htmlFor="notes">Notes:</label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleChange}
              />
            </div>
            <div className="form-actions">
              <button type="button" onClick={() => setShowModal(false)}>Cancel</button>
              <button type="submit">Save Appointment</button>
            </div>
          </form>
        </div>
      </div>
    );
  };

  const ListView = () => (
    <div className="appointments-list">
      <table>
        <thead>
          <tr>
            <th>Patient</th>
            <th>Doctor</th>
            <th>Department</th>
            <th>Date & Time</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {appointments.map((appointment) => (
            <tr key={appointment._id}>
              <td>{appointment.patient?.first_name} {appointment.patient?.last_name}</td>
              <td>Dr. {appointment.doctor?.first_name} {appointment.doctor?.last_name}</td>
              <td>{appointment.department?.name}</td>
              <td>
                {new Date(appointment.appointment_date).toLocaleDateString()} at {appointment.appointment_time}
              </td>
              <td>
                <span className={`status ${appointment.status}`}>
                  {appointment.status}
                </span>
              </td>
              <td>
                <button onClick={() => handleEditAppointment(appointment)} className="btn-edit">
                  Edit
                </button>
                <button onClick={() => handleDeleteAppointment(appointment._id)} className="btn-delete">
                  Delete
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  const CalendarView = () => (
    <div className="calendar-view">
      <div className="calendar-header">
        <button onClick={() => console.log('Previous week')}>Previous</button>
        <h3>Week of {new Date().toLocaleDateString()}</h3>
        <button onClick={() => console.log('Next week')}>Next</button>
      </div>
      <div className="calendar-grid">
        {/* In a real app, you would implement a full calendar component */}
        <div className="calendar-day">
          <h4>Monday</h4>
          <ul>
            <li>9:00 AM - John Doe with Dr. Smith</li>
            <li>11:00 AM - Jane Smith with Dr. Johnson</li>
          </ul>
        </div>
        <div className="calendar-day">
          <h4>Tuesday</h4>
          <ul>
            <li>2:00 PM - Bob Brown with Dr. Smith</li>
          </ul>
        </div>
        {/* Add more days as needed */}
      </div>
    </div>
  );

  if (loading) return <div>Loading appointments...</div>;
  if (error) return <div className="error-message">{error}</div>;

  return (
    <div className="appointments-container">
      <div className="appointments-header">
        <h2>Appointments</h2>
        <div className="appointments-actions">
          <div className="view-toggle">
            <button 
              className={view === 'list' ? 'active' : ''} 
              onClick={() => setView('list')}
            >
              List View
            </button>
            <button 
              className={view === 'calendar' ? 'active' : ''} 
              onClick={() => setView('calendar')}
            >
              Calendar View
            </button>
          </div>
          <button onClick={handleCreateAppointment} className="btn-primary">
            Schedule Appointment
          </button>
        </div>
      </div>
      
      {view === 'list' ? <ListView /> : <CalendarView />}
      
      {showModal && <AppointmentModal />}
    </div>
  );
};

export default Appointments;