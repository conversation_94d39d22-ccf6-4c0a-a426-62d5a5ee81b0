import React, { useState, useEffect } from 'react';

const Inventory = () => {
  const [inventory, setInventory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [search, setSearch] = useState('');
  const [filter, setFilter] = useState('all'); // 'all', 'drug', 'medical_supply', 'equipment', 'other'
  const [lowStockOnly, setLowStockOnly] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showAdjustModal, setShowAdjustModal] = useState(false);
  const [currentItem, setCurrentItem] = useState(null);
  const [adjustmentData, setAdjustmentData] = useState({ quantity: 0, reason: '' });

  // Fetch inventory from API
  useEffect(() => {
    fetchInventory();
  }, [filter, lowStockOnly]);

  const fetchInventory = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      // Build query parameters
      const params = new URLSearchParams();
      if (filter !== 'all') params.append('category', filter);
      if (lowStockOnly) params.append('lowStock', 'true');
      
      const response = await fetch(`/api/inventory?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setInventory(data.data);
      } else {
        setError(data.error || 'Failed to fetch inventory');
      }
    } catch (err) {
      setError('An error occurred while fetching inventory');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateItem = () => {
    setCurrentItem(null);
    setShowModal(true);
  };

  const handleEditItem = (item) => {
    setCurrentItem(item);
    setShowModal(true);
  };

  const handleDeleteItem = async (id) => {
    if (window.confirm('Are you sure you want to delete this inventory item?')) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`/api/inventory/${id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
        
        if (response.ok) {
          fetchInventory(); // Refresh the list
        } else {
          const data = await response.json();
          setError(data.error || 'Failed to delete inventory item');
        }
      } catch (err) {
        setError('An error occurred while deleting inventory item');
      }
    }
  };

  const handleAdjustStock = (item) => {
    setCurrentItem(item);
    setAdjustmentData({ quantity: item.quantity, reason: '' });
    setShowAdjustModal(true);
  };

  const submitAdjustment = async (e) => {
    e.preventDefault();
    
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/inventory/${currentItem._id}/adjust`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(adjustmentData),
      });
      
      const data = await response.json();
      
      if (response.ok) {
        fetchInventory(); // Refresh the list
        setShowAdjustModal(false);
      } else {
        setError(data.error || 'Failed to adjust inventory');
      }
    } catch (err) {
      setError('An error occurred while adjusting inventory');
    }
  };

  const InventoryModal = () => {
    const [formData, setFormData] = useState({
      name: '',
      description: '',
      category: 'drug',
      unit: '',
      quantity: 0,
      reorder_level: 10,
      unit_price: 0,
      supplier: '',
      expiry_date: '',
      batch_number: '',
      is_active: true,
    });

    useEffect(() => {
      if (currentItem) {
        setFormData({
          name: currentItem.name || '',
          description: currentItem.description || '',
          category: currentItem.category || 'drug',
          unit: currentItem.unit || '',
          quantity: currentItem.quantity || 0,
          reorder_level: currentItem.reorder_level || 10,
          unit_price: currentItem.unit_price || 0,
          supplier: currentItem.supplier?._id || '',
          expiry_date: currentItem.expiry_date ? currentItem.expiry_date.split('T')[0] : '',
          batch_number: currentItem.batch_number || '',
          is_active: currentItem.is_active !== undefined ? currentItem.is_active : true,
        });
      } else {
        setFormData({
          name: '',
          description: '',
          category: 'drug',
          unit: '',
          quantity: 0,
          reorder_level: 10,
          unit_price: 0,
          supplier: '',
          expiry_date: '',
          batch_number: '',
          is_active: true,
        });
      }
    }, [currentItem]);

    const handleChange = (e) => {
      setFormData({
        ...formData,
        [e.target.name]: e.target.value,
      });
    };

    const handleSubmit = async (e) => {
      e.preventDefault();
      
      try {
        const token = localStorage.getItem('token');
        const url = currentItem ? `/api/inventory/${currentItem._id}` : '/api/inventory';
        const method = currentItem ? 'PUT' : 'POST';
        
        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify(formData),
        });
        
        const data = await response.json();
        
        if (response.ok) {
          fetchInventory(); // Refresh the list
          setShowModal(false);
        } else {
          setError(data.error || 'Failed to save inventory item');
        }
      } catch (err) {
        setError('An error occurred while saving inventory item');
      }
    };

    return (
      <div className="modal">
        <div className="modal-content">
          <span className="close" onClick={() => setShowModal(false)}>&times;</span>
          <h2>{currentItem ? 'Edit Inventory Item' : 'Add New Inventory Item'}</h2>
          <form onSubmit={handleSubmit}>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="name">Name:</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="category">Category:</label>
                <select
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                >
                  <option value="drug">Drug</option>
                  <option value="medical_supply">Medical Supply</option>
                  <option value="equipment">Equipment</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>
            <div className="form-group">
              <label htmlFor="description">Description:</label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
              />
            </div>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="unit">Unit:</label>
                <input
                  type="text"
                  id="unit"
                  name="unit"
                  value={formData.unit}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="quantity">Quantity:</label>
                <input
                  type="number"
                  id="quantity"
                  name="quantity"
                  value={formData.quantity}
                  onChange={handleChange}
                  min="0"
                  required
                />
              </div>
            </div>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="reorder_level">Reorder Level:</label>
                <input
                  type="number"
                  id="reorder_level"
                  name="reorder_level"
                  value={formData.reorder_level}
                  onChange={handleChange}
                  min="0"
                />
              </div>
              <div className="form-group">
                <label htmlFor="unit_price">Unit Price:</label>
                <input
                  type="number"
                  id="unit_price"
                  name="unit_price"
                  value={formData.unit_price}
                  onChange={handleChange}
                  step="0.01"
                  min="0"
                />
              </div>
            </div>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="supplier">Supplier:</label>
                <select
                  id="supplier"
                  name="supplier"
                  value={formData.supplier}
                  onChange={handleChange}
                >
                  <option value="">Select Supplier</option>
                  {/* In a real app, you would fetch suppliers from the API */}
                  <option value="supplier1">MediSupply Ltd</option>
                  <option value="supplier2">PharmaCorp</option>
                </select>
              </div>
              <div className="form-group">
                <label htmlFor="expiry_date">Expiry Date:</label>
                <input
                  type="date"
                  id="expiry_date"
                  name="expiry_date"
                  value={formData.expiry_date}
                  onChange={handleChange}
                />
              </div>
            </div>
            <div className="form-group">
              <label htmlFor="batch_number">Batch Number:</label>
              <input
                type="text"
                id="batch_number"
                name="batch_number"
                value={formData.batch_number}
                onChange={handleChange}
              />
            </div>
            <div className="form-group">
              <label htmlFor="is_active">Active:</label>
              <input
                type="checkbox"
                id="is_active"
                name="is_active"
                checked={formData.is_active}
                onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
              />
            </div>
            <div className="form-actions">
              <button type="button" onClick={() => setShowModal(false)}>Cancel</button>
              <button type="submit">Save Item</button>
            </div>
          </form>
        </div>
      </div>
    );
  };

  const AdjustStockModal = () => (
    <div className="modal">
      <div className="modal-content">
        <span className="close" onClick={() => setShowAdjustModal(false)}>&times;</span>
        <h2>Adjust Stock: {currentItem?.name}</h2>
        <form onSubmit={submitAdjustment}>
          <div className="form-group">
            <label htmlFor="quantity">New Quantity:</label>
            <input
              type="number"
              id="quantity"
              value={adjustmentData.quantity}
              onChange={(e) => setAdjustmentData({ ...adjustmentData, quantity: parseInt(e.target.value) })}
              min="0"
              required
            />
          </div>
          <div className="form-group">
            <label htmlFor="reason">Reason for Adjustment:</label>
            <textarea
              id="reason"
              value={adjustmentData.reason}
              onChange={(e) => setAdjustmentData({ ...adjustmentData, reason: e.target.value })}
              required
            />
          </div>
          <div className="form-actions">
            <button type="button" onClick={() => setShowAdjustModal(false)}>Cancel</button>
            <button type="submit">Adjust Stock</button>
          </div>
        </form>
      </div>
    </div>
  );

  const filteredInventory = inventory.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(search.toLowerCase()) ||
                         item.description.toLowerCase().includes(search.toLowerCase());
    
    const isLowStock = item.quantity <= item.reorder_level;
    
    return matchesSearch && (!lowStockOnly || isLowStock);
  });

  if (loading) return <div>Loading inventory...</div>;
  if (error) return <div className="error-message">{error}</div>;

  return (
    <div className="inventory-container">
      <div className="inventory-header">
        <h2>Inventory</h2>
        <div className="inventory-actions">
          <div className="filters">
            <input
              type="text"
              placeholder="Search inventory..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="search-input"
            />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Categories</option>
              <option value="drug">Drugs</option>
              <option value="medical_supply">Medical Supplies</option>
              <option value="equipment">Equipment</option>
              <option value="other">Other</option>
            </select>
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={lowStockOnly}
                onChange={(e) => setLowStockOnly(e.target.checked)}
              />
              Low Stock Only
            </label>
          </div>
          <button onClick={handleCreateItem} className="btn-primary">
            Add New Item
          </button>
        </div>
      </div>
      
      <div className="inventory-list">
        <table>
          <thead>
            <tr>
              <th>Name</th>
              <th>Category</th>
              <th>Unit</th>
              <th>Quantity</th>
              <th>Reorder Level</th>
              <th>Unit Price</th>
              <th>Supplier</th>
              <th>Expiry Date</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredInventory.map((item) => (
              <tr key={item._id} className={item.quantity <= item.reorder_level ? 'low-stock' : ''}>
                <td>{item.name}</td>
                <td>{item.category}</td>
                <td>{item.unit}</td>
                <td>{item.quantity}</td>
                <td>{item.reorder_level}</td>
                <td>${item.unit_price.toFixed(2)}</td>
                <td>{item.supplier?.name || 'N/A'}</td>
                <td>{item.expiry_date ? new Date(item.expiry_date).toLocaleDateString() : 'N/A'}</td>
                <td>
                  {item.quantity <= item.reorder_level ? (
                    <span className="status low-stock">Low Stock</span>
                  ) : (
                    <span className="status active">Active</span>
                  )}
                </td>
                <td>
                  <button onClick={() => handleAdjustStock(item)} className="btn-edit">
                    Adjust
                  </button>
                  <button onClick={() => handleEditItem(item)} className="btn-edit">
                    Edit
                  </button>
                  <button onClick={() => handleDeleteItem(item._id)} className="btn-delete">
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {showModal && <InventoryModal />}
      {showAdjustModal && <AdjustStockModal />}
    </div>
  );
};

export default Inventory;