import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import Login from './components/Login';
import Dashboard from './components/Dashboard';
import Patients from './components/Patients';
import Appointments from './components/Appointments';
import Admissions from './components/Admissions';
import Inventory from './components/Inventory';
import Billing from './components/Billing';
import LabTests from './components/LabTests';
import MpesaReceipts from './components/MpesaReceipts';
import Reports from './components/Reports';
import Staff from './components/Staff';
import './App.css';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <Routes>
            <Route path="/" element={<Navigate to="/dashboard" />} />
            <Route path="/login" element={<Login />} />
            <Route path="/unauthorized" element={<div>Unauthorized Access</div>} />
            
            {/* Protected routes */}
            <Route 
              path="/dashboard" 
              element={
                <ProtectedRoute>
                  <Dashboard />
                </ProtectedRoute>
              } 
            />
            
            {/* Admin, Doctor, Nurse, Clerk routes */}
            <Route 
              path="/patients" 
              element={
                <ProtectedRoute roles={['admin', 'doctor', 'nurse', 'clerk']}>
                  <Patients />
                </ProtectedRoute>
              } 
            />
            
            <Route 
              path="/appointments" 
              element={
                <ProtectedRoute roles={['admin', 'doctor', 'nurse', 'clerk']}>
                  <Appointments />
                </ProtectedRoute>
              } 
            />
            
            <Route 
              path="/admissions" 
              element={
                <ProtectedRoute roles={['admin', 'doctor', 'nurse']}>
                  <Admissions />
                </ProtectedRoute>
              } 
            />
            
            <Route 
              path="/inventory" 
              element={
                <ProtectedRoute roles={['admin', 'doctor', 'nurse', 'clerk']}>
                  <Inventory />
                </ProtectedRoute>
              } 
            />
            
            <Route 
              path="/billing" 
              element={
                <ProtectedRoute roles={['admin', 'clerk']}>
                  <Billing />
                </ProtectedRoute>
              } 
            />
            
            <Route 
              path="/lab-tests" 
              element={
                <ProtectedRoute roles={['admin', 'doctor', 'nurse']}>
                  <LabTests />
                </ProtectedRoute>
              } 
            />
            
            <Route 
              path="/mpesa-receipts" 
              element={
                <ProtectedRoute roles={['admin', 'clerk']}>
                  <MpesaReceipts />
                </ProtectedRoute>
              } 
            />
            
            <Route 
              path="/reports" 
              element={
                <ProtectedRoute roles={['admin', 'doctor', 'nurse', 'clerk']}>
                  <Reports />
                </ProtectedRoute>
              } 
            />
            
            {/* Admin only routes */}
            <Route 
              path="/staff" 
              element={
                <ProtectedRoute roles={['admin']}>
                  <Staff />
                </ProtectedRoute>
              } 
            />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
