const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const mongoose = require('mongoose');
const rateLimit = require('express-rate-limit');

// Load environment variables
dotenv.config();

// Create Express app
const app = express();

// Rate limiting
const limiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 60, // limit each IP to 60 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.'
  }
});

// Apply rate limiting to all requests
app.use(limiter);

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Connect to MongoDB
mongoose.connect(process.env.DB_URL || 'mongodb://localhost:27017/hms', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => console.log('Connected to MongoDB'))
.catch((err) => console.error('MongoDB connection error:', err));

// Health check endpoint
app.use('/health', require('./src/routes/health.routes'));

// Routes
app.get('/', (req, res) => {
  res.json({ 
    message: 'Hospital Management System API',
    version: '1.0.0'
  });
});

// Auth routes
app.use('/auth', require('./src/routes/auth.routes'));

// API routes
app.use('/api/patients', require('./src/routes/patients.routes'));
app.use('/api/staff', require('./src/routes/staff.routes'));
app.use('/api/departments', require('./src/routes/departments.routes'));
app.use('/api/appointments', require('./src/routes/appointments.routes'));
app.use('/api/beds', require('./src/routes/beds.routes'));
app.use('/api/admissions', require('./src/routes/admissions.routes'));
app.use('/api/inventory', require('./src/routes/inventory.routes'));
app.use('/api/suppliers', require('./src/routes/suppliers.routes'));
app.use('/api/invoices', require('./src/routes/invoices.routes'));
app.use('/api/payments', require('./src/routes/payments.routes'));
app.use('/api/lab-orders', require('./src/routes/lab-orders.routes'));
app.use('/api/reports', require('./src/routes/reports.routes'));
app.use('/api/mpesa', require('./src/routes/mpesa.routes'));

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ 
    error: 'Something went wrong!',
    message: err.message 
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

module.exports = app;