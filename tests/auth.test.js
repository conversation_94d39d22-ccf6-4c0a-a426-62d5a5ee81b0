const request = require('supertest');
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const app = require('../server');
const Staff = require('../src/models/Staff');
const { connectTestDB, disconnectTestDB } = require('./setup');

describe('Authentication API', () => {
  beforeAll(async () => {
    await connectTestDB();
  });

  afterAll(async () => {
    await Staff.deleteMany({});
    await disconnectTestDB();
  });

  describe('POST /auth/login', () => {
    const staffData = {
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      phone: '1234567890',
      role: 'admin',
      password: 'password123',
    };

    beforeEach(async () => {
      // Create a staff member for testing
      const hashedPassword = await bcrypt.hash(staffData.password, 10);
      await Staff.create({ ...staffData, password: hashedPassword });
    });

    afterEach(async () => {
      // Clean up test data
      await Staff.deleteMany({});
    });

    it('should login successfully with valid credentials', async () => {
      const response = await request(app)
        .post('/auth/login')
        .send({
          email: staffData.email,
          password: staffData.password,
        })
        .expect(200);

      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('staff');
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data.staff.email).toBe(staffData.email);
      expect(response.body.error).toBeNull();
    });

    it('should fail login with invalid email', async () => {
      const response = await request(app)
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: staffData.password,
        })
        .expect(401);

      expect(response.body.error).toBe('Invalid credentials');
    });

    it('should fail login with invalid password', async () => {
      const response = await request(app)
        .post('/auth/login')
        .send({
          email: staffData.email,
          password: 'wrongpassword',
        })
        .expect(401);

      expect(response.body.error).toBe('Invalid credentials');
    });

    it('should fail login with missing email', async () => {
      const response = await request(app)
        .post('/auth/login')
        .send({
          password: staffData.password,
        })
        .expect(400);

      expect(response.body.error).toBe('Email and password are required');
    });

    it('should fail login with missing password', async () => {
      const response = await request(app)
        .post('/auth/login')
        .send({
          email: staffData.email,
        })
        .expect(400);

      expect(response.body.error).toBe('Email and password are required');
    });
  });

  describe('GET /auth/verify', () => {
    let token;
    const staffData = {
      first_name: 'Jane',
      last_name: 'Smith',
      email: '<EMAIL>',
      phone: '0987654321',
      role: 'doctor',
      password: 'password123',
    };

    beforeEach(async () => {
      // Create a staff member and get token
      const hashedPassword = await bcrypt.hash(staffData.password, 10);
      const staff = await Staff.create({ ...staffData, password: hashedPassword });
      
      // Generate token
      const jwt = require('jsonwebtoken');
      token = jwt.sign(
        { 
          id: staff._id, 
          email: staff.email, 
          role: staff.role 
        },
        process.env.JWT_SECRET || 'test_secret',
        { expiresIn: '1d' }
      );
    });

    afterEach(async () => {
      // Clean up test data
      await Staff.deleteMany({});
    });

    it('should verify token successfully', async () => {
      const response = await request(app)
        .get('/auth/verify')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('staff');
      expect(response.body.data.staff.email).toBe(staffData.email);
      expect(response.body.error).toBeNull();
    });

    it('should fail verification with invalid token', async () => {
      const response = await request(app)
        .get('/auth/verify')
        .set('Authorization', 'Bearer invalidtoken')
        .expect(400);

      expect(response.body.error).toBe('Invalid token.');
    });

    it('should fail verification with missing token', async () => {
      const response = await request(app)
        .get('/auth/verify')
        .expect(401);

      expect(response.body.error).toBe('Access denied. No token provided.');
    });
  });
});