const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Set test environment
process.env.NODE_ENV = 'test';

// Connect to test database
const connectTestDB = async () => {
  try {
    await mongoose.connect(process.env.TEST_DB_URL || 'mongodb://localhost:27017/hms_test', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('Connected to test database');
  } catch (error) {
    console.error('Error connecting to test database:', error);
    process.exit(1);
  }
};

// Disconnect from test database
const disconnectTestDB = async () => {
  try {
    await mongoose.connection.close();
    console.log('Disconnected from test database');
  } catch (error) {
    console.error('Error disconnecting from test database:', error);
  }
};

module.exports = {
  connectTestDB,
  disconnectTestDB,
};