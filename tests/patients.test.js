const request = require('supertest');
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const app = require('../server');
const Patient = require('../src/models/Patient');
const Staff = require('../src/models/Staff');
const { connectTestDB, disconnectTestDB } = require('./setup');

describe('Patients API', () => {
  let adminToken, doctorToken, clerkToken;
  const patientData = {
    first_name: '<PERSON>',
    last_name: 'Doe',
    dob: '1980-01-01',
    gender: 'Male',
    phone: '**********',
    email: '<EMAIL>',
    address: '123 Main St',
  };

  beforeAll(async () => {
    await connectTestDB();
    
    // Create staff members for testing
    const adminStaff = new Staff({
      first_name: 'Admin',
      last_name: 'User',
      email: '<EMAIL>',
      phone: '**********',
      role: 'admin',
      password: await bcrypt.hash('admin123', 10),
    });
    
    const doctorStaff = new Staff({
      first_name: 'Doctor',
      last_name: 'User',
      email: '<EMAIL>',
      phone: '**********',
      role: 'doctor',
      password: await bcrypt.hash('doctor123', 10),
    });
    
    const clerkStaff = new Staff({
      first_name: 'Clerk',
      last_name: 'User',
      email: '<EMAIL>',
      phone: '**********',
      role: 'clerk',
      password: await bcrypt.hash('clerk123', 10),
    });
    
    await adminStaff.save();
    await doctorStaff.save();
    await clerkStaff.save();
    
    // Generate tokens
    const jwt = require('jsonwebtoken');
    adminToken = jwt.sign(
      { id: adminStaff._id, email: adminStaff.email, role: adminStaff.role },
      process.env.JWT_SECRET || 'test_secret',
      { expiresIn: '1d' }
    );
    
    doctorToken = jwt.sign(
      { id: doctorStaff._id, email: doctorStaff.email, role: doctorStaff.role },
      process.env.JWT_SECRET || 'test_secret',
      { expiresIn: '1d' }
    );
    
    clerkToken = jwt.sign(
      { id: clerkStaff._id, email: clerkStaff.email, role: clerkStaff.role },
      process.env.JWT_SECRET || 'test_secret',
      { expiresIn: '1d' }
    );
  });

  afterAll(async () => {
    await Patient.deleteMany({});
    await Staff.deleteMany({});
    await disconnectTestDB();
  });

  describe('GET /patients', () => {
    beforeEach(async () => {
      // Create test patients
      await Patient.create([
        { ...patientData, first_name: 'John', last_name: 'Doe', phone: '**********' },
        { ...patientData, first_name: 'Jane', last_name: 'Smith', phone: '**********' },
      ]);
    });

    afterEach(async () => {
      await Patient.deleteMany({});
    });

    it('should get all patients with admin token', async () => {
      const response = await request(app)
        .get('/api/patients')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('meta');
      expect(response.body.data).toHaveLength(2);
      expect(response.body.error).toBeNull();
    });

    it('should get all patients with doctor token', async () => {
      const response = await request(app)
        .get('/api/patients')
        .set('Authorization', `Bearer ${doctorToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('meta');
      expect(response.body.data).toHaveLength(2);
      expect(response.body.error).toBeNull();
    });

    it('should get all patients with clerk token', async () => {
      const response = await request(app)
        .get('/api/patients')
        .set('Authorization', `Bearer ${clerkToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('meta');
      expect(response.body.data).toHaveLength(2);
      expect(response.body.error).toBeNull();
    });

    it('should search patients by name', async () => {
      const response = await request(app)
        .get('/api/patients?search=John')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].first_name).toBe('John');
    });

    it('should paginate patients', async () => {
      const response = await request(app)
        .get('/api/patients?page=1&limit=1')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data).toHaveLength(1);
      expect(response.body.meta).toHaveProperty('page', 1);
      expect(response.body.meta).toHaveProperty('limit', 1);
      expect(response.body.meta).toHaveProperty('total', 2);
    });
  });

  describe('POST /patients', () => {
    afterEach(async () => {
      await Patient.deleteMany({});
    });

    it('should create a new patient with admin token', async () => {
      const response = await request(app)
        .post('/api/patients')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(patientData)
        .expect(201);

      expect(response.body).toHaveProperty('data');
      expect(response.body.data.first_name).toBe(patientData.first_name);
      expect(response.body.data.last_name).toBe(patientData.last_name);
      expect(response.body.error).toBeNull();
    });

    it('should create a new patient with clerk token', async () => {
      const response = await request(app)
        .post('/api/patients')
        .set('Authorization', `Bearer ${clerkToken}`)
        .send({ ...patientData, phone: '**********' })
        .expect(201);

      expect(response.body).toHaveProperty('data');
      expect(response.body.data.first_name).toBe(patientData.first_name);
      expect(response.body.error).toBeNull();
    });

    it('should fail to create patient with doctor token', async () => {
      const response = await request(app)
        .post('/api/patients')
        .set('Authorization', `Bearer ${doctorToken}`)
        .send(patientData)
        .expect(403);

      expect(response.body.error).toBe('Access denied. Insufficient permissions.');
    });

    it('should fail to create patient with missing required fields', async () => {
      const response = await request(app)
        .post('/api/patients')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ first_name: 'John' })
        .expect(400);

      expect(response.body.error).toBeDefined();
    });
  });

  describe('PUT /patients/:id', () => {
    let patientId;

    beforeEach(async () => {
      const patient = new Patient(patientData);
      await patient.save();
      patientId = patient._id;
    });

    afterEach(async () => {
      await Patient.deleteMany({});
    });

    it('should update patient with admin token', async () => {
      const response = await request(app)
        .put(`/api/patients/${patientId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ first_name: 'Updated John' })
        .expect(200);

      expect(response.body).toHaveProperty('data');
      expect(response.body.data.first_name).toBe('Updated John');
      expect(response.body.error).toBeNull();
    });

    it('should update patient with clerk token', async () => {
      const response = await request(app)
        .put(`/api/patients/${patientId}`)
        .set('Authorization', `Bearer ${clerkToken}`)
        .send({ first_name: 'Updated John' })
        .expect(200);

      expect(response.body).toHaveProperty('data');
      expect(response.body.data.first_name).toBe('Updated John');
      expect(response.body.error).toBeNull();
    });

    it('should fail to update patient with doctor token', async () => {
      const response = await request(app)
        .put(`/api/patients/${patientId}`)
        .set('Authorization', `Bearer ${doctorToken}`)
        .send({ first_name: 'Updated John' })
        .expect(403);

      expect(response.body.error).toBe('Access denied. Insufficient permissions.');
    });

    it('should fail to update non-existent patient', async () => {
      const fakeId = mongoose.Types.ObjectId();
      const response = await request(app)
        .put(`/api/patients/${fakeId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ first_name: 'Updated John' })
        .expect(404);

      expect(response.body.error).toBe('Patient not found');
    });
  });

  describe('DELETE /patients/:id', () => {
    let patientId;

    beforeEach(async () => {
      const patient = new Patient(patientData);
      await patient.save();
      patientId = patient._id;
    });

    afterEach(async () => {
      await Patient.deleteMany({});
    });

    it('should delete patient with admin token', async () => {
      const response = await request(app)
        .delete(`/api/patients/${patientId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('data');
      expect(response.body.data.message).toBe('Patient deleted successfully');
      expect(response.body.error).toBeNull();
    });

    it('should fail to delete patient with clerk token', async () => {
      const response = await request(app)
        .delete(`/api/patients/${patientId}`)
        .set('Authorization', `Bearer ${clerkToken}`)
        .expect(403);

      expect(response.body.error).toBe('Access denied. Insufficient permissions.');
    });

    it('should fail to delete non-existent patient', async () => {
      const fakeId = mongoose.Types.ObjectId();
      const response = await request(app)
        .delete(`/api/patients/${fakeId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);

      expect(response.body.error).toBe('Patient not found');
    });
  });
});