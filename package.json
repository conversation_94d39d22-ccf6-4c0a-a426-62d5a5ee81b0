{"name": "hms-magic-app", "version": "1.0.0", "description": "Hospital Management System with Admin Console and REST API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "cd client && npm run build", "client": "cd client && npm start", "dev:full": "concurrently \"npm run dev\" \"npm run client\"", "test": "jest"}, "keywords": ["hospital", "management", "api", "admin"], "author": "HMS Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express-rate-limit": "^6.10.0", "nodemailer": "^6.9.4", "axios": "^1.5.0", "joi": "^17.9.2", "winston": "^3.10.0", "node-cron": "^3.0.2", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0"}, "devDependencies": {"nodemon": "^3.0.1", "concurrently": "^8.2.0", "jest": "^29.6.2"}}